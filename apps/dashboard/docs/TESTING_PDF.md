# Testing PDF Generation

This document provides instructions for testing the PDF generation functionality.

## Prerequisites

1. Make sure dependencies are installed:
   ```bash
   cd apps/dashboard
   pnpm install
   ```

2. Ensure the local storage directory exists:
   ```bash
   mkdir -p apps/dashboard/public/invoices
   ```

3. Start the development server:
   ```bash
   pnpm dev
   ```

## Test Scenarios

### 1. PDF Preview in Invoice Form

**Steps:**
1. Navigate to invoice creation page
2. Fill out all required fields:
   - Invoice details (dates are auto-filled)
   - Buyer information (select existing contractor or add new)
   - Add at least one line item with description, quantity, price
   - Payment method and other details
3. Click the "Podgląd" (Preview) button
4. **Expected:** PDF should open in a new browser tab/window

**What to verify:**
- PDF contains organization name and details
- Invoice number is displayed
- Buyer information is correct
- Line items table is properly formatted
- Totals are calculated correctly
- Polish formatting is applied (dates, currency)

### 2. Automatic PDF Generation on Invoice Creation

**Steps:**
1. Create a complete invoice (as above)
2. Click "Wystaw fakturę" (Issue Invoice) button
3. Wait for success message
4. Navigate to the invoice detail page

**Expected:**
- Invoice is created successfully
- PDF is automatically generated and stored
- PDF actions (View, Download, Regenerate) are available

### 3. PDF Actions in Invoice Detail View

**Steps:**
1. Open an existing invoice with PDF
2. Test each PDF action button:
   - **View PDF:** Should open PDF in new tab
   - **Download PDF:** Should download PDF file
   - **Regenerate PDF:** Should create new PDF

### 4. PDF Actions in Invoice Modal

**Steps:**
1. Go to invoices list page
2. Click on an invoice to open detail modal
3. Test PDF actions in the modal

## Troubleshooting

### Common Issues

1. **"Server-only" Error:**
   - This should be fixed with the API endpoint approach
   - Check browser console for specific errors

2. **PDF Generation Fails:**
   - Check browser console for JavaScript errors
   - Verify all required form fields are filled
   - Check network tab for API call failures

3. **PDF Not Displaying Correctly:**
   - Verify organization data is complete
   - Check if logo is properly formatted (base64 or URL)
   - Ensure line items have all required fields

4. **File Storage Issues:**
   - Check if `public/invoices/` directory exists
   - Verify file permissions
   - Check server logs for file write errors

### Debug Information

**Browser Console:**
- Check for JavaScript errors
- Look for PDF generation logs
- Verify API calls are successful

**Network Tab:**
- Monitor `/api/organization/details` calls
- Check for 500 errors or failed requests

**Server Logs:**
- Look for file storage errors
- Check PDF generation action logs
- Verify database updates

## Expected File Structure After Testing

```
apps/dashboard/public/invoices/
├── {uuid-1}.pdf
├── {uuid-2}.pdf
└── ...
```

## Performance Notes

- PDF preview is generated client-side (faster)
- PDF storage is server-side (persistent)
- Large invoices with many line items should still generate quickly
- Logo loading might add slight delay to preview

## Browser Compatibility

- Modern browsers with PDF support
- JavaScript enabled
- Local storage access
- File download capabilities

## Next Steps

After successful testing:
1. Consider adding PDF email functionality
2. Implement S3 storage for production
3. Add PDF templates/customization
4. Optimize for mobile devices
5. Add PDF compression for large files
