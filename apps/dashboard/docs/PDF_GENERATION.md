# Invoice PDF Generation

This document describes the PDF generation system implemented for invoices in the dashboard application.

## Overview

The PDF generation system allows users to:
- Generate PDF invoices automatically when creating invoices
- Preview PDFs before saving invoices
- Download existing PDFs
- Regenerate PDFs when invoice data changes

## Architecture

### Components

1. **PDF Generator** (`lib/pdf-generator.ts`)
   - Core PDF generation using jsPDF and jsPDF-autotable
   - Handles Polish formatting and layout
   - Supports organization logos
   - Professional invoice layout with proper spacing

2. **File Storage Service** (`lib/file-storage-service.ts`)
   - Abstraction layer for file storage
   - Currently implements local file storage
   - Designed to be easily replaceable with S3 or other cloud providers
   - Stores PDFs in `public/invoices/` directory

3. **Server Actions** (`actions/financial-documents/generate-pdf-action.ts`)
   - `generateInvoicePdfAction`: Creates PDF and stores it
   - `regenerateInvoicePdfAction`: Updates existing PDFs
   - Handles organization data retrieval and PDF data preparation

4. **UI Components** (`components/financial-documents/pdf-actions.tsx`)
   - `PdfActions`: Complete PDF management interface
   - `PdfPreviewButton`: Simple preview button
   - `PdfStatusIndicator`: Shows PDF availability status

### Data Flow

1. **Invoice Creation**:
   - User creates invoice through form
   - If status is not DRAFT, PDF is automatically generated
   - PDF is stored locally and URL is saved to database

2. **PDF Preview**:
   - User clicks preview button in form
   - Form data is validated
   - PDF is generated client-side and opened in new window
   - No storage occurs for previews

3. **PDF Download/View**:
   - User clicks view/download buttons
   - PDF is served from local storage
   - Browser handles display/download

## Local Storage Implementation

### Current Setup
- PDFs stored in: `apps/dashboard/public/invoices/`
- File naming: `{uuid}.pdf`
- URL format: `{baseUrl}/invoices/{filename}`
- Gitignore: `*.pdf` files excluded from version control

### File Organization
```
public/
└── invoices/
    ├── {uuid-1}.pdf
    ├── {uuid-2}.pdf
    └── ...
```

## Future S3 Integration

The system is designed for easy migration to S3:

### Planned Changes
1. **Environment Variables**:
   ```
   AWS_REGION=us-east-1
   AWS_ACCESS_KEY_ID=your-key
   AWS_SECRET_ACCESS_KEY=your-secret
   S3_BUCKET_NAME=your-bucket
   S3_BUCKET_REGION=us-east-1
   ```

2. **S3 Service Implementation**:
   - Replace `LocalFileStorageService` with `S3FileStorageService`
   - Implement signed URLs for secure access
   - Add lifecycle policies for cleanup
   - Consider CDN integration for performance

3. **Configuration**:
   ```typescript
   // In file-storage-service.ts
   export function createFileStorageService(): FileStorageService {
     if (process.env.NODE_ENV === 'production' && process.env.S3_BUCKET_NAME) {
       return new S3FileStorageService({
         bucket: process.env.S3_BUCKET_NAME,
         region: process.env.S3_BUCKET_REGION,
         // ... other config
       });
     }
     return new LocalFileStorageService(config);
   }
   ```

## PDF Features

### Current Features
- Professional Polish invoice layout
- Organization logo support (when available)
- Proper address formatting
- VAT calculations and display
- Line items table with proper formatting
- Payment information
- Polish date and currency formatting
- Automatic generation timestamp

### Layout Structure
1. **Header**: Logo (left) + Title + Invoice number (center)
2. **Parties**: Seller (left) + Buyer (right) with addresses
3. **Invoice Details**: Dates, payment method, etc.
4. **Line Items**: Table with descriptions, quantities, prices, VAT
5. **Totals**: Net, VAT, and Gross amounts
6. **Payment Info**: Bank account, notes
7. **Footer**: Generation timestamp

## Usage Examples

### In Invoice Form (Preview)
```typescript
import { PdfPreviewButton } from '~/components/financial-documents/pdf-actions';

<PdfPreviewButton 
  invoiceId={invoiceId}
  invoiceNumber={invoiceNumber}
/>
```

### In Invoice Detail View
```typescript
import { PdfActions } from '~/components/financial-documents/pdf-actions';

<PdfActions
  invoiceId={invoice.id}
  invoiceNumber={invoice.invoiceNumber}
  existingPdfUrl={invoice.documentPdfUrl}
/>
```

### Server-side Generation
```typescript
import { generateInvoicePdfAction } from '~/actions/financial-documents/generate-pdf-action';

const result = await generateInvoicePdfAction({ invoiceId });
if (result.success) {
  console.log('PDF URL:', result.data.pdfUrl);
}
```

## Dependencies

- `jspdf`: Core PDF generation library
- `jspdf-autotable`: Table generation for line items
- `uuid`: Unique filename generation
- `sharp`: Image processing (for logos)

## Error Handling

- PDF generation failures don't block invoice creation
- Client-side validation before PDF preview
- Graceful fallbacks for missing organization data
- Proper error messages for users
- Console logging for debugging

## Performance Considerations

- PDF generation is asynchronous
- Large invoices with many line items are handled efficiently
- Logo images are optimized before embedding
- Local storage provides fast access for development
- S3 integration will add CDN benefits for production

## Security

- Local files served through Next.js static file serving
- Future S3 implementation will use signed URLs
- No sensitive data exposed in PDF URLs
- Proper access control through organization membership
