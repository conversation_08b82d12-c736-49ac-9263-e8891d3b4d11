export const INVOICE_FORM_LABELS = {
  title: 'Wystaw fakturę',

  // Section headers
  sections: {
    invoiceDetails: 'Szczegóły faktury',
    parties: 'Strony umowy',
    invoiceItems: 'Pozycje faktury',
    totals: 'Podsumowanie',
    additionalInfo: 'Dodatkowe informacje'
  },

  // Form fields
  fields: {
    // Invoice Details Section
    invoiceNumber: 'Numer faktury',
    issueDate: 'Data wystawienia',
    saleDate: 'Data sprzedaży',
    dueDate: '<PERSON><PERSON><PERSON> pła<PERSON>',

    // Parties Section
    seller: 'Sprzedawca',
    buyer: 'Nabywca',
    contractorSelect: 'Wyszukaj istniejącego kontrahenta',
    addNewContractor: 'Dodaj nowego kontrahenta',
    saveAsNewContractor: 'Zapisz jako nowego kontrahenta',
    companyName: 'Nazwa firmy / Imię i nazwisko',
    nip: 'NIP',
    street: 'Ulica',
    postalCode: 'Kod pocztowy',
    city: 'Miasto',
    country: '<PERSON>raj',
    email: 'Email',

    // Invoice Items Section
    productService: 'Nazwa towaru/usługi',
    quantity: 'Ilość',
    unitOfMeasure: 'Jednostka miary',
    netPrice: 'Cena netto',
    vatRate: 'Stawka VAT',
    discount: 'Rabat (%)',

    // Totals Section
    netTotal: 'Suma netto',
    vatTotal: 'Suma VAT',
    grossTotal: 'Suma brutto',
    currency: 'Waluta',

    // Additional Info Section
    paymentMethod: 'Sposób płatności',
    bankAccount: 'Numer konta bankowego',
    orderNumber: 'Numer zamówienia / Referencja klienta',
    notes: 'Uwagi / Dodatkowe informacje',
    attachment: 'Załącznik'
  },

  // Helper texts
  helpers: {
    invoiceNumber: 'Numer faktury może być edytowany ręcznie lub wygenerowany automatycznie.',
    saleDate: 'Data sprzedaży musi być wcześniejsza lub równa dacie wystawienia.',
    dueDate: 'Opcjonalny termin płatności, domyślnie 14 dni od daty wystawienia.',
    nip: 'NIP jest opcjonalny, ale pomaga w identyfikacji.',
    quantity: 'Ilość musi być większa od zera.',
    discount: 'Rabat to opcjonalny procent stosowany do pozycji.',
    currency: 'Domyślna waluta to PLN, ale można wybrać inne waluty.',
    paymentMethod: 'Wybierz sposób płatności (np. przelew, karta, gotówka).',
    bankAccount: 'Edytowalny lub automatycznie wypełniany z profilu firmy.'
  },

  // Buttons
  buttons: {
    addItem: 'Dodaj pozycję',
    removeItem: 'Usuń pozycję',
    saveAsDraft: 'Zapisz jako szkic',
    issueInvoice: 'Wystaw fakturę',
    preview: 'Podgląd',
    cancel: 'Anuluj',
    addContractor: 'Dodaj kontrahenta'
  },

  // Validation messages
  validation: {
    required: 'To pole jest wymagane',
    invalidEmail: 'Nieprawidłowy adres email',
    invalidDate: 'Nieprawidłowa data',
    saleDateAfterIssue: 'Data sprzedaży nie może być późniejsza niż data wystawienia',
    positiveNumber: 'Wartość musi być większa od zera',
    nonNegativeNumber: 'Wartość nie może być ujemna',
    invalidCurrency: 'Waluta musi być kodem 3-literowym',
    minOneItem: 'Faktura musi zawierać co najmniej jedną pozycję',
    discountRange: 'Rabat musi być między 0 a 100%'
  },

  // Options
  options: {
    currencies: [
      { value: 'PLN', label: 'PLN' },
      { value: 'EUR', label: 'EUR' },
      { value: 'USD', label: 'USD' },
      { value: 'GBP', label: 'GBP' },
      { value: 'CHF', label: 'CHF' }
    ],

    vatRates: [
      { value: 0, label: 'zw.' },
      { value: 5, label: '5%' },
      { value: 8, label: '8%' },
      { value: 23, label: '23%' }
    ],

    paymentMethods: [
      { value: 'TRANSFER', label: 'Przelew bankowy' },
      { value: 'CASH', label: 'Gotówka' },
      { value: 'CARD', label: 'Karta płatnicza' },
      { value: 'BARTER', label: 'Barter' },
      { value: 'OTHER', label: 'Inne' }
    ],

    unitsOfMeasure: [
      { value: 'szt.', label: 'szt.' },
      { value: 'kg', label: 'kg' },
      { value: 'l', label: 'l' },
      { value: 'm', label: 'm' },
      { value: 'm²', label: 'm²' },
      { value: 'm³', label: 'm³' },
      { value: 'godz.', label: 'godz.' },
      { value: 'usługa', label: 'usługa' },
      { value: 'komplet', label: 'komplet' },
      { value: 'opakowanie', label: 'opakowanie' }
    ]
  },

  // Empty states
  emptyStates: {
    noContractors: 'Brak kontrahentów',
    noItems: 'Brak pozycji na fakturze'
  },

  // Success messages
  success: {
    draftSaved: 'Szkic faktury został zapisany',
    invoiceCreated: 'Faktura została wystawiona',
    contractorAdded: 'Kontrahent został dodany'
  }
} as const;
