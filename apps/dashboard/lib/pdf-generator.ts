/**
 * PDF Generation Service for Invoices
 * 
 * This service handles the generation of PDF invoices using jsPDF
 * with proper Polish formatting and layout.
 */

import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { formatPolishDate, formatPolishCurrency, formatNIP } from './polish-formatters';
import type { CreateInvoiceSchema } from '~/schemas/financial-documents/create-invoice-schema';

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

interface InvoicePDFData extends CreateInvoiceSchema {
  organization: {
    name: string;
    nip?: string;
    street?: string;
    city?: string;
    postalCode?: string;
    country?: string;
    email?: string;
    phone?: string;
    logo?: string; // Base64 encoded logo or URL
    legalForm?: string;
  };
  contractor?: {
    fullName: string;
    nip?: string;
    street?: string;
    city?: string;
    postalCode?: string;
    country?: string;
    email?: string;
  };
  bankAccount?: string; // Bank account number for payments
}

export class InvoicePDFGenerator {
  private doc: jsPDF;
  private pageWidth: number;
  private pageHeight: number;
  private margin: number = 20;

  constructor() {
    this.doc = new jsPDF();
    this.pageWidth = this.doc.internal.pageSize.getWidth();
    this.pageHeight = this.doc.internal.pageSize.getHeight();
  }

  /**
   * Generate PDF for an invoice
   */
  generateInvoicePDF(data: InvoicePDFData): Blob {
    this.addHeader(data);
    this.addParties(data);
    this.addInvoiceDetails(data);
    this.addLineItems(data);
    this.addTotals(data);
    this.addPaymentInfo(data);
    this.addFooter(data);

    return this.doc.output('blob');
  }

  /**
   * Generate PDF and return as base64 string
   */
  generateInvoicePDFBase64(data: InvoicePDFData): string {
    this.generateInvoicePDF(data);
    return this.doc.output('datauristring');
  }

  /**
   * Preview PDF in new window
   */
  previewInvoicePDF(data: InvoicePDFData): void {
    this.generateInvoicePDF(data);
    const pdfUrl = this.doc.output('bloburl');
    window.open(pdfUrl, '_blank');
  }

  private addHeader(data: InvoicePDFData): void {
    let yPos = 20;

    // Add logo if available
    if (data.organization.logo) {
      try {
        // Add logo on the left side
        this.doc.addImage(data.organization.logo, 'PNG', this.margin, yPos, 40, 20);
      } catch (error) {
        console.warn('Failed to add logo to PDF:', error);
      }
    }

    // Title
    this.doc.setFontSize(24);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('FAKTURA', this.pageWidth / 2, yPos + 15, { align: 'center' });

    // Invoice number
    this.doc.setFontSize(16);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text(`Nr: ${data.invoiceNumber}`, this.pageWidth / 2, yPos + 30, { align: 'center' });
  }

  private addParties(data: InvoicePDFData): void {
    let yPos = 70;

    // Seller (left side)
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('SPRZEDAWCA:', this.margin, yPos);

    this.doc.setFont('helvetica', 'normal');
    yPos += 10;
    this.doc.text(data.organization.name, this.margin, yPos);

    if (data.organization.nip) {
      yPos += 7;
      this.doc.text(`NIP: ${formatNIP(data.organization.nip)}`, this.margin, yPos);
    }

    // Build organization address from components
    const orgAddress = this.buildAddress(
      data.organization.street,
      data.organization.postalCode,
      data.organization.city,
      data.organization.country
    );
    if (orgAddress) {
      yPos += 7;
      this.doc.text(orgAddress, this.margin, yPos);
    }

    // Buyer (right side)
    yPos = 70;
    const rightX = this.pageWidth / 2 + 10;

    this.doc.setFont('helvetica', 'bold');
    this.doc.text('NABYWCA:', rightX, yPos);

    this.doc.setFont('helvetica', 'normal');
    yPos += 10;

    const buyerName = data.contractor?.fullName || data.newContractor?.companyName || 'Nieznany nabywca';
    this.doc.text(buyerName, rightX, yPos);

    const buyerNip = data.contractor?.nip || data.newContractor?.nip;
    if (buyerNip) {
      yPos += 7;
      this.doc.text(`NIP: ${formatNIP(buyerNip)}`, rightX, yPos);
    }

    // Build buyer address from components
    const buyerAddress = this.buildAddress(
      data.contractor?.street || data.newContractor?.street,
      data.contractor?.postalCode || data.newContractor?.postalCode,
      data.contractor?.city || data.newContractor?.city,
      data.contractor?.country || data.newContractor?.country
    );
    if (buyerAddress) {
      yPos += 7;
      this.doc.text(buyerAddress, rightX, yPos);
    }
  }

  private addInvoiceDetails(data: InvoicePDFData): void {
    const yPos = 140;
    
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'normal');
    
    // Invoice details in a table format
    const details = [
      ['Data wystawienia:', formatPolishDate(data.issueDate)],
      ['Data sprzedaży:', formatPolishDate(data.saleDate)],
      ['Termin płatności:', data.dueDate ? formatPolishDate(data.dueDate) : 'Nie określono'],
      ['Sposób płatności:', this.getPaymentMethodLabel(data.paymentMethod)]
    ];

    details.forEach(([label, value], index) => {
      const y = yPos + (index * 7);
      this.doc.text(label, this.margin, y);
      this.doc.text(value, this.margin + 60, y);
    });
  }

  private addLineItems(data: InvoicePDFData): void {
    const startY = 180;
    
    // Prepare table data
    const headers = [
      'LP',
      'Nazwa towaru/usługi',
      'Ilość',
      'J.m.',
      'Cena netto',
      'Rabat %',
      'Wartość netto',
      'VAT %',
      'Kwota VAT',
      'Wartość brutto'
    ];

    const rows = data.lineItems.map((item, index) => [
      (index + 1).toString(),
      item.description,
      item.quantity.toString(),
      item.unitOfMeasure,
      formatPolishCurrency(item.netPrice, data.currency),
      item.discount ? `${item.discount}%` : '0%',
      formatPolishCurrency(item.netAmount, data.currency),
      `${item.vatRate}%`,
      formatPolishCurrency(item.vatAmount, data.currency),
      formatPolishCurrency(item.grossAmount, data.currency)
    ]);

    this.doc.autoTable({
      head: [headers],
      body: rows,
      startY: startY,
      styles: {
        fontSize: 8,
        cellPadding: 2
      },
      headStyles: {
        fillColor: [240, 240, 240],
        textColor: [0, 0, 0],
        fontStyle: 'bold'
      },
      columnStyles: {
        0: { halign: 'center', cellWidth: 15 }, // LP
        1: { cellWidth: 50 }, // Nazwa
        2: { halign: 'right', cellWidth: 20 }, // Ilość
        3: { halign: 'center', cellWidth: 15 }, // J.m.
        4: { halign: 'right', cellWidth: 25 }, // Cena netto
        5: { halign: 'right', cellWidth: 20 }, // Rabat
        6: { halign: 'right', cellWidth: 25 }, // Wartość netto
        7: { halign: 'right', cellWidth: 20 }, // VAT %
        8: { halign: 'right', cellWidth: 25 }, // Kwota VAT
        9: { halign: 'right', cellWidth: 30 } // Wartość brutto
      }
    });
  }

  private addTotals(data: InvoicePDFData): void {
    const finalY = (this.doc as any).lastAutoTable.finalY + 20;
    
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'bold');
    
    const rightX = this.pageWidth - this.margin - 80;
    
    // Totals
    this.doc.text('Razem netto:', rightX, finalY);
    this.doc.text(formatPolishCurrency(data.netAmount, data.currency), rightX + 40, finalY, { align: 'right' });
    
    this.doc.text('Razem VAT:', rightX, finalY + 10);
    this.doc.text(formatPolishCurrency(data.vatAmount, data.currency), rightX + 40, finalY + 10, { align: 'right' });
    
    this.doc.setFontSize(14);
    this.doc.text('RAZEM BRUTTO:', rightX, finalY + 25);
    this.doc.text(formatPolishCurrency(data.grossAmount, data.currency), rightX + 40, finalY + 25, { align: 'right' });
  }

  private addPaymentInfo(data: InvoicePDFData): void {
    const yPos = this.pageHeight - 80;

    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'normal');

    const accountNumber = data.paymentAccountNumber || data.bankAccount;
    if (accountNumber) {
      this.doc.text('Numer konta:', this.margin, yPos);
      this.doc.text(accountNumber, this.margin + 40, yPos);
    }

    if (data.notes) {
      this.doc.text('Uwagi:', this.margin, yPos + 15);
      const splitNotes = this.doc.splitTextToSize(data.notes, this.pageWidth - 2 * this.margin);
      this.doc.text(splitNotes, this.margin, yPos + 25);
    }
  }

  private addFooter(_data: InvoicePDFData): void {
    const yPos = this.pageHeight - 30;

    this.doc.setFontSize(8);
    this.doc.setFont('helvetica', 'normal');
    this.doc.text(
      `Faktura wygenerowana automatycznie - ${formatPolishDate(new Date())}`,
      this.pageWidth / 2,
      yPos,
      { align: 'center' }
    );
  }

  private buildAddress(street?: string, postalCode?: string, city?: string, country?: string): string {
    const parts: string[] = [];

    if (street) parts.push(street);
    if (postalCode && city) {
      parts.push(`${postalCode} ${city}`);
    } else if (city) {
      parts.push(city);
    }
    if (country && country !== 'Polska' && country !== 'Poland') {
      parts.push(country);
    }

    return parts.join(', ');
  }

  private getPaymentMethodLabel(method: string): string {
    const methods: Record<string, string> = {
      'TRANSFER': 'Przelew bankowy',
      'CASH': 'Gotówka',
      'CARD': 'Karta płatnicza',
      'BARTER': 'Barter',
      'OTHER': 'Inne',
      // Also support lowercase for backward compatibility
      'transfer': 'Przelew bankowy',
      'cash': 'Gotówka',
      'card': 'Karta płatnicza',
      'barter': 'Barter',
      'other': 'Inne'
    };
    return methods[method] || method;
  }
}

// Export singleton instance
export const invoicePDFGenerator = new InvoicePDFGenerator();
