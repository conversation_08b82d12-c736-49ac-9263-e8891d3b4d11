/**
 * File Storage Service
 * 
 * This service provides an abstraction layer for file storage.
 * Currently implements local file storage, but designed to be easily
 * replaceable with S3 or other cloud storage providers.
 */

import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface FileStorageConfig {
  baseUrl: string;
  localStoragePath: string;
}

export interface StoredFile {
  url: string;
  key: string;
  size: number;
}

export interface FileStorageService {
  uploadFile(buffer: Buffer, fileName: string, contentType: string): Promise<StoredFile>;
  deleteFile(key: string): Promise<void>;
  getFileUrl(key: string): string;
  fileExists(key: string): Promise<boolean>;
}

/**
 * Local File Storage Implementation
 * Stores files locally in the public directory for development
 */
export class LocalFileStorageService implements FileStorageService {
  private config: FileStorageConfig;

  constructor(config: FileStorageConfig) {
    this.config = config;
  }

  async uploadFile(buffer: Buffer, fileName: string, contentType: string): Promise<StoredFile> {
    try {
      // Generate unique key for the file
      const fileExtension = path.extname(fileName);
      const uniqueFileName = `${uuidv4()}${fileExtension}`;
      const key = `invoices/${uniqueFileName}`;
      
      // Ensure directory exists
      const fullPath = path.join(this.config.localStoragePath, key);
      const directory = path.dirname(fullPath);
      await fs.mkdir(directory, { recursive: true });
      
      // Write file
      await fs.writeFile(fullPath, buffer);
      
      // Return file info
      return {
        url: `${this.config.baseUrl}/${key}`,
        key,
        size: buffer.length
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async deleteFile(key: string): Promise<void> {
    try {
      const fullPath = path.join(this.config.localStoragePath, key);
      await fs.unlink(fullPath);
    } catch (error) {
      // Don't throw if file doesn't exist
      if ((error as any)?.code !== 'ENOENT') {
        console.error('Error deleting file:', error);
        throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  }

  getFileUrl(key: string): string {
    return `${this.config.baseUrl}/${key}`;
  }

  async fileExists(key: string): Promise<boolean> {
    try {
      const fullPath = path.join(this.config.localStoragePath, key);
      await fs.access(fullPath);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * S3 File Storage Implementation (Future)
 * This will be implemented when switching to S3
 */
export class S3FileStorageService implements FileStorageService {
  constructor(config: any) {
    throw new Error('S3 storage not implemented yet. Use LocalFileStorageService for now.');
  }

  async uploadFile(buffer: Buffer, fileName: string, contentType: string): Promise<StoredFile> {
    throw new Error('S3 storage not implemented yet');
  }

  async deleteFile(key: string): Promise<void> {
    throw new Error('S3 storage not implemented yet');
  }

  getFileUrl(key: string): string {
    throw new Error('S3 storage not implemented yet');
  }

  async fileExists(key: string): Promise<boolean> {
    throw new Error('S3 storage not implemented yet');
  }
}

/**
 * Factory function to create the appropriate storage service
 */
export function createFileStorageService(): FileStorageService {
  // For now, always use local storage
  // Later this can be configured via environment variables
  const config: FileStorageConfig = {
    baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    localStoragePath: path.join(process.cwd(), 'public')
  };

  return new LocalFileStorageService(config);
}

// Export singleton instance
export const fileStorageService = createFileStorageService();
