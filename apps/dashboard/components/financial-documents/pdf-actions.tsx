'use client';

import * as React from 'react';
import { Button } from '@workspace/ui/components/button';
import { toast } from '@workspace/ui/components/toast';
import { 
  EyeIcon, 
  DownloadIcon, 
  FileTextIcon, 
  RefreshCwIcon,
  LoaderIcon 
} from 'lucide-react';
import { generateInvoicePdfAction, regenerateInvoicePdfAction } from '~/actions/financial-documents/generate-pdf-action';

interface PdfActionsProps {
  invoiceId: string;
  invoiceNumber: string;
  existingPdfUrl?: string | null;
  className?: string;
}

export function PdfActions({ 
  invoiceId, 
  invoiceNumber, 
  existingPdfUrl, 
  className 
}: PdfActionsProps) {
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [isRegenerating, setIsRegenerating] = React.useState(false);
  const [pdfUrl, setPdfUrl] = React.useState(existingPdfUrl);

  const handleGeneratePdf = async () => {
    setIsGenerating(true);
    try {
      const result = await generateInvoicePdfAction({ invoiceId });
      
      if (result?.success && result.data) {
        setPdfUrl(result.data.pdfUrl);
        toast.success('PDF został wygenerowany pomyślnie');
      } else {
        toast.error(result?.error || 'Nie udało się wygenerować PDF');
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Wystąpił błąd podczas generowania PDF');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleRegeneratePdf = async () => {
    setIsRegenerating(true);
    try {
      const result = await regenerateInvoicePdfAction({ invoiceId });
      
      if (result?.success && result.data) {
        setPdfUrl(result.data.pdfUrl);
        toast.success('PDF został ponownie wygenerowany');
      } else {
        toast.error(result?.error || 'Nie udało się ponownie wygenerować PDF');
      }
    } catch (error) {
      console.error('Error regenerating PDF:', error);
      toast.error('Wystąpił błąd podczas ponownego generowania PDF');
    } finally {
      setIsRegenerating(false);
    }
  };

  const handleViewPdf = () => {
    if (pdfUrl) {
      window.open(pdfUrl, '_blank');
    }
  };

  const handleDownloadPdf = () => {
    if (pdfUrl) {
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = `faktura-${invoiceNumber.replace(/\//g, '-')}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className={`flex flex-wrap gap-2 ${className || ''}`}>
      {!pdfUrl ? (
        <Button
          variant="outline"
          size="sm"
          onClick={handleGeneratePdf}
          disabled={isGenerating}
        >
          {isGenerating ? (
            <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <FileTextIcon className="mr-2 h-4 w-4" />
          )}
          {isGenerating ? 'Generowanie...' : 'Generuj PDF'}
        </Button>
      ) : (
        <>
          <Button
            variant="outline"
            size="sm"
            onClick={handleViewPdf}
          >
            <EyeIcon className="mr-2 h-4 w-4" />
            Podgląd PDF
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadPdf}
          >
            <DownloadIcon className="mr-2 h-4 w-4" />
            Pobierz PDF
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRegeneratePdf}
            disabled={isRegenerating}
          >
            {isRegenerating ? (
              <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCwIcon className="mr-2 h-4 w-4" />
            )}
            {isRegenerating ? 'Regenerowanie...' : 'Regeneruj PDF'}
          </Button>
        </>
      )}
    </div>
  );
}

interface PdfPreviewButtonProps {
  invoiceId: string;
  invoiceNumber: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

export function PdfPreviewButton({
  invoiceId,
  invoiceNumber,
  variant = 'outline',
  size = 'sm',
  className
}: PdfPreviewButtonProps) {
  const [isGenerating, setIsGenerating] = React.useState(false);

  const handlePreview = async () => {
    setIsGenerating(true);
    try {
      // For existing invoices, generate PDF on server and open
      const result = await generateInvoicePdfAction({ invoiceId });

      if (result?.success && result.data) {
        window.open(result.data.pdfUrl, '_blank');
      } else {
        toast.error(result?.error || 'Nie udało się wygenerować podglądu PDF');
      }
    } catch (error) {
      console.error('Error generating PDF preview:', error);
      toast.error('Wystąpił błąd podczas generowania podglądu');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handlePreview}
      disabled={isGenerating}
      className={className}
    >
      {isGenerating ? (
        <LoaderIcon className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <EyeIcon className="mr-2 h-4 w-4" />
      )}
      {isGenerating ? 'Generowanie...' : 'Podgląd PDF'}
    </Button>
  );
}

interface PdfStatusIndicatorProps {
  pdfUrl?: string | null;
  className?: string;
}

export function PdfStatusIndicator({ pdfUrl, className }: PdfStatusIndicatorProps) {
  return (
    <div className={`flex items-center gap-1 ${className || ''}`}>
      <div 
        className={`w-2 h-2 rounded-full ${
          pdfUrl ? 'bg-green-500' : 'bg-gray-300'
        }`} 
      />
      <span className="text-xs text-muted-foreground">
        {pdfUrl ? 'PDF dostępny' : 'Brak PDF'}
      </span>
    </div>
  );
}
