'use client';

import * as React from 'react';
import NiceModal, { useModal, type NiceModalHocProps } from '@ebay/nice-modal-react';
import { XIcon, EditIcon, EyeIcon, DownloadIcon, CopyIcon, FileTextIcon, TrashIcon } from 'lucide-react';
import { useMediaQuery } from '@workspace/ui/hooks/use-media-query';

import { Badge } from '@workspace/ui/components/badge';
import { Button } from '@workspace/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@workspace/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle
} from '@workspace/ui/components/drawer';
import { Separator } from '@workspace/ui/components/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@workspace/ui/components/table';

import { formatPolishDate, formatPolishCurrency, formatNIP, formatBankAccount } from '~/lib/polish-formatters';
import { INCOME_LABELS } from '~/lib/income-constants';
import { MediaQueries } from '@workspace/ui/lib/media-queries';
import type { FinancialDocumentDto } from '~/types/dtos/financial-document-dto';
import type { CategoryDto } from '~/types/dtos/category-dto';
import type { ContractorDto } from '~/types/dtos/contractor-dto';
import { PdfActions } from './pdf-actions';

export type InvoiceDetailModalProps = NiceModalHocProps & {
  document: FinancialDocumentDto;
  categories: CategoryDto[];
  contractors: ContractorDto[];
};

function InvoiceDetailModal({
  document,
  categories,
  contractors
}: InvoiceDetailModalProps) {
  const modal = useModal();
  const mdUp = useMediaQuery(MediaQueries.MdUp);

  // Helper function to convert amount to number
  const toNumber = (amount: number | string | null | undefined): number => {
    if (amount === null || amount === undefined) return 0;
    return typeof amount === 'string' ? parseFloat(amount) || 0 : amount;
  };

  // Get category and contractor details
  const category = categories.find(cat => cat.id === document.categoryId);
  const contractor = contractors.find(cont => cont.id === document.contractorId);

  // Status display
  const getStatusDisplay = (paymentStatus: string) => {
    switch (paymentStatus) {
      case 'paid':
        return { label: INCOME_LABELS.statuses.paid, color: 'bg-green-100 text-green-800' };
      case 'unpaid':
        return { label: INCOME_LABELS.statuses.unpaid, color: 'bg-orange-100 text-orange-800' };
      case 'overdue':
        return { label: INCOME_LABELS.statuses.overdue, color: 'bg-red-100 text-red-800' };
      default:
        return { label: INCOME_LABELS.statuses.draft, color: 'bg-gray-100 text-gray-800' };
    }
  };

  const statusDisplay = getStatusDisplay(document.paymentStatus);

  // Document type display
  const getDocumentTypeDisplay = (documentType: string) => {
    switch (documentType) {
      case 'invoice':
        return INCOME_LABELS.documentTypes.invoice;
      case 'receipt':
        return INCOME_LABELS.documentTypes.receipt;
      case 'receipt_with_vat':
        return INCOME_LABELS.documentTypes.receiptWithVat;
      case 'correction_invoice':
        return INCOME_LABELS.documentTypes.correctionInvoice;
      default:
        return documentType;
    }
  };

  const handleClose = () => {
    modal.hide();
  };

  const renderContent = (
    <div className="space-y-6">
      {/* Header with Edit Button */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">{INCOME_LABELS.modal.title}</h2>
        <Button variant="outline" size="sm">
          <EditIcon className="mr-2 h-4 w-4" />
          {INCOME_LABELS.modal.edit}
        </Button>
      </div>

      {/* Basic Information */}
      <div>
        <h3 className="text-base font-medium mb-3">{INCOME_LABELS.modal.basicInfo}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">{INCOME_LABELS.fields.documentNumber}:</span>
            <span className="ml-2 font-medium">{document.invoiceNumber}</span>
          </div>
          <div>
            <span className="text-muted-foreground">{INCOME_LABELS.fields.issueDate}:</span>
            <span className="ml-2">{formatPolishDate(new Date(document.issueDate))}</span>
          </div>
          <div>
            <span className="text-muted-foreground">{INCOME_LABELS.fields.saleDate}:</span>
            <span className="ml-2">{formatPolishDate(new Date(document.saleDate))}</span>
          </div>
          <div>
            <span className="text-muted-foreground">{INCOME_LABELS.fields.dueDate}:</span>
            <span className="ml-2">{formatPolishDate(new Date(document.dueDate))}</span>
          </div>
          <div>
            <span className="text-muted-foreground">Typ dokumentu:</span>
            <span className="ml-2">{getDocumentTypeDisplay(document.documentType)}</span>
          </div>
          <div>
            <span className="text-muted-foreground">{INCOME_LABELS.fields.status}:</span>
            <Badge className={`ml-2 ${statusDisplay.color}`}>
              {statusDisplay.label}
            </Badge>
          </div>
          {category && (
            <div>
              <span className="text-muted-foreground">{INCOME_LABELS.fields.category}:</span>
              <Badge
                className="ml-2"
                style={{ backgroundColor: category.color + '20', color: category.color }}
              >
                {category.name}
              </Badge>
            </div>
          )}
          {document.isCompanyExpense && (
            <div>
              <span className="text-muted-foreground">{INCOME_LABELS.fields.qualifiedAmount}:</span>
              <span className="ml-2">{formatPolishCurrency(toNumber(document.grossAmount), document.currency)}</span>
            </div>
          )}
        </div>
      </div>

      <Separator />

      {/* Buyer and Seller Information */}
      <div>
        <h3 className="text-base font-medium mb-3">{INCOME_LABELS.modal.parties}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Buyer (Client) */}
          <div>
            <h4 className="font-medium text-sm mb-2">Nabywca</h4>
            <div className="space-y-2 text-sm">
              <div>
                <span className="text-muted-foreground">{INCOME_LABELS.fields.contractor}:</span>
                <div className="font-medium">{document.clientName}</div>
                {document.clientVatId && (
                  <div className="text-muted-foreground">NIP: {formatNIP(document.clientVatId)}</div>
                )}
              </div>
              {contractor && (
                <>
                  <div>
                    <span className="text-muted-foreground">{INCOME_LABELS.fields.companyName}:</span>
                    <div>{contractor.fullName}</div>
                  </div>
                  {(contractor.street || contractor.city || contractor.postalCode || contractor.country) && (
                    <div>
                      <span className="text-muted-foreground">{INCOME_LABELS.fields.address}:</span>
                      <div>
                        {contractor.street && <div>{contractor.street}</div>}
                        {(contractor.postalCode || contractor.city) && (
                          <div>
                            {contractor.postalCode && `${contractor.postalCode} `}
                            {contractor.city}
                          </div>
                        )}
                        {contractor.country && <div>{contractor.country}</div>}
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>

          {/* Seller (Organization) */}
          <div>
            <h4 className="font-medium text-sm mb-2">Sprzedawca</h4>
            <div className="space-y-2 text-sm">
              <div>
                <span className="text-muted-foreground">{INCOME_LABELS.fields.sellerName}:</span>
                <div className="font-medium">Twoja firma</div>
              </div>
              {document.paymentAccountNumber && (
                <div>
                  <span className="text-muted-foreground">{INCOME_LABELS.fields.bankAccount}:</span>
                  <div className="font-mono text-xs">{formatBankAccount(document.paymentAccountNumber)}</div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Line Items */}
      <div>
        <h3 className="text-base font-medium mb-3">{INCOME_LABELS.modal.lineItems}</h3>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">{INCOME_LABELS.lineItemColumns.lp}</TableHead>
                <TableHead>{INCOME_LABELS.lineItemColumns.name}</TableHead>
                <TableHead className="text-right">{INCOME_LABELS.lineItemColumns.netPrice}</TableHead>
                <TableHead className="text-right">{INCOME_LABELS.lineItemColumns.netValue}</TableHead>
                <TableHead className="text-center">{INCOME_LABELS.lineItemColumns.vat}</TableHead>
                <TableHead className="text-right">{INCOME_LABELS.lineItemColumns.vatValue}</TableHead>
                <TableHead className="text-right">{INCOME_LABELS.lineItemColumns.grossValue}</TableHead>
                <TableHead className="text-center">{INCOME_LABELS.lineItemColumns.businessExpense}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {document.lineItems && document.lineItems.length > 0 ? (
                document.lineItems.map((item, index) => (
                  <TableRow key={item.id || index}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{item.description}</TableCell>
                    <TableCell className="text-right">{formatPolishCurrency(toNumber(item.unitPrice))}</TableCell>
                    <TableCell className="text-right">{formatPolishCurrency(toNumber(item.netAmount))}</TableCell>
                    <TableCell className="text-center">{item.vatRate}%</TableCell>
                    <TableCell className="text-right">{formatPolishCurrency(toNumber(item.vatAmount))}</TableCell>
                    <TableCell className="text-right">{formatPolishCurrency(toNumber(item.grossAmount))}</TableCell>
                    <TableCell className="text-center">
                      {item.isBusinessExpense === 'yes' ? '✓' :
                       item.isBusinessExpense === 'verification' ? '?' : '-'}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-4 text-muted-foreground">
                    Brak pozycji na fakturze
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Summary Totals */}
        <div className="mt-4 flex justify-end">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between gap-8">
              <span className="text-muted-foreground">{INCOME_LABELS.fields.netValue}:</span>
              <span className="font-medium">{formatPolishCurrency(toNumber(document.netAmount), document.currency)}</span>
            </div>
            <div className="flex justify-between gap-8">
              <span className="text-muted-foreground">{INCOME_LABELS.fields.vatValue}:</span>
              <span className="font-medium">{formatPolishCurrency(toNumber(document.vatAmount), document.currency)}</span>
            </div>
            <div className="flex justify-between gap-8 border-t pt-2">
              <span className="font-medium">{INCOME_LABELS.fields.grossValue}:</span>
              <span className="font-bold">{formatPolishCurrency(toNumber(document.grossAmount), document.currency)}</span>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Actions */}
      <div>
        <h3 className="text-base font-medium mb-3">{INCOME_LABELS.modal.invoice}</h3>
        <div className="flex flex-wrap gap-2">
          <PdfActions
            invoiceId={document.id}
            invoiceNumber={document.invoiceNumber}
            existingPdfUrl={document.documentPdfUrl}
          />
          <Button variant="outline" size="sm">
            <CopyIcon className="mr-2 h-4 w-4" />
            {INCOME_LABELS.modal.actions.duplicate}
          </Button>
          <Button variant="outline" size="sm">
            <FileTextIcon className="mr-2 h-4 w-4" />
            {INCOME_LABELS.modal.actions.correct}
          </Button>
          <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
            <TrashIcon className="mr-2 h-4 w-4" />
            {INCOME_LABELS.modal.actions.delete}
          </Button>
        </div>
      </div>
    </div>
  );

  if (mdUp) {
    return (
      <Dialog open={modal.visible} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              {INCOME_LABELS.modal.title}
              <Button variant="ghost" size="icon" onClick={handleClose}>
                <XIcon className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="px-6 py-4 overflow-y-auto max-h-[calc(90vh-120px)]">
            {renderContent}
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={modal.visible} onOpenChange={handleClose}>
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle className="flex items-center justify-between">
            {INCOME_LABELS.modal.title}
            <Button variant="ghost" size="icon" onClick={handleClose}>
              <XIcon className="h-4 w-4" />
            </Button>
          </DrawerTitle>
        </DrawerHeader>
        <div className="px-4 py-4 overflow-y-auto max-h-[calc(100vh-120px)]">
          {renderContent}
        </div>
      </DrawerContent>
    </Drawer>
  );
}

export default NiceModal.create(InvoiceDetailModal);
