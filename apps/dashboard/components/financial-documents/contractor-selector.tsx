'use client';

import * as React from 'react';
import { Check, ChevronsUpDown, Plus } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

import { Button } from '@workspace/ui/components/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@workspace/ui/components/command';
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage
} from '@workspace/ui/components/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@workspace/ui/components/popover';
import { Separator } from '@workspace/ui/components/separator';
import { cn } from '@workspace/ui/lib/utils';

import { INVOICE_FORM_LABELS } from '~/lib/invoice-form-constants';
import type { ContractorDto } from '~/types/dtos/contractor-dto';
import type { CreateInvoiceSchema } from '~/schemas/financial-documents/create-invoice-schema';
import ContractorModal from '~/components/contractors/contractor-modal';
import NiceModal from '@ebay/nice-modal-react';

interface ContractorSelectorProps {
  form: UseFormReturn<CreateInvoiceSchema>;
  contractors: ContractorDto[];
  disabled?: boolean;
}

export function ContractorSelector({
  form,
  contractors,
  disabled = false
}: ContractorSelectorProps): React.JSX.Element {
  const [open, setOpen] = React.useState(false);

  const selectedContractorId = form.watch('contractorId');
  const selectedContractor = contractors.find(c => c.id === selectedContractorId);

  const handleSelectContractor = (contractorId: string) => {
    form.setValue('contractorId', contractorId);
    form.setValue('newContractor', undefined);
    setOpen(false);
  };

  // Register the modal component
  React.useEffect(() => {
    try {
      // First try to remove any existing modal to prevent duplicates
      try {
        NiceModal.remove('contractor-modal');
      } catch (_) {
        // Ignore errors
      }

      // Register the modal
      NiceModal.register('contractor-modal', ContractorModal);
    } catch (error) {
      console.error('Error registering modal:', error);
    }

    // Clean up on unmount
    return () => {
      try {
        NiceModal.remove('contractor-modal');
      } catch (_) {
        // Ignore errors
      }
    };
  }, []);

  const handleAddNewContractor = () => {
    // Show the modal
    NiceModal.show('contractor-modal', {
      onSuccess: (newContractor?: ContractorDto) => {
        if (newContractor) {
          // Set the newly created contractor as selected
          form.setValue('contractorId', newContractor.id);
          form.setValue('newContractor', undefined);
          // Trigger form validation
          form.trigger(['contractorId', 'newContractor']);
        } else {
          // Fallback: refresh the page to get updated contractors list
          window.location.reload();
        }
      }
    });
  };

  return (
    <FormField
      control={form.control}
      name="contractorId"
      render={({ field }) => (
        <FormItem>
          <div className="flex space-x-2">
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className={cn(
                      'w-full justify-between',
                      !field.value && 'text-muted-foreground'
                    )}
                    disabled={disabled}
                  >
                    {selectedContractor
                      ? selectedContractor.fullName
                      : INVOICE_FORM_LABELS.fields.contractorSelect}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0">
                <Command>
                  <CommandInput placeholder={INVOICE_FORM_LABELS.fields.contractorSelect} />
                  <CommandList>
                    <CommandEmpty>{INVOICE_FORM_LABELS.emptyStates.noContractors}</CommandEmpty>
                    <CommandGroup>
                      {contractors.map((contractor) => (
                        <CommandItem
                          key={contractor.id}
                          value={contractor.fullName}
                          onSelect={() => handleSelectContractor(contractor.id)}
                        >
                          <Check
                            className={cn(
                              'mr-2 h-4 w-4',
                              selectedContractorId === contractor.id
                                ? 'opacity-100'
                                : 'opacity-0'
                            )}
                          />
                          <div className="flex flex-col">
                            <span className="font-medium">{contractor.fullName}</span>
                            {contractor.nip && (
                              <span className="text-sm text-muted-foreground">
                                NIP: {contractor.nip}
                              </span>
                            )}
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                    <Separator />
                    <CommandGroup>
                      <CommandItem onSelect={handleAddNewContractor}>
                        <Plus className="mr-2 h-4 w-4" />
                        {INVOICE_FORM_LABELS.fields.addNewContractor}
                      </CommandItem>
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={handleAddNewContractor}
              disabled={disabled}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
