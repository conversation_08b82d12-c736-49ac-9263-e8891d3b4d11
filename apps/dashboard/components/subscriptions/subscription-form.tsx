'use client';

import * as React from 'react';
import { useRouter } from 'next/navigation';
import NiceModal from '@ebay/nice-modal-react';
import { PlusCircle as PlusCircleIcon } from 'lucide-react';

// Define enum values directly since we can't import them from Prisma
const SubscriptionType = {
  INCOME: 'income',
  EXPENSE: 'expense'
};

const SubscriptionFrequency = {
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
  SEMI_ANNUAL: 'semi_annual',
  ANNUAL: 'annual'
};

const SubscriptionStatus = {
  ACTIVE: 'active',
  ENDED: 'ended',
  SUSPENDED: 'suspended',
  TRIAL: 'trial'
};

import { Button } from '@workspace/ui/components/button';
import { Checkbox } from '@workspace/ui/components/checkbox';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormProvider
} from '@workspace/ui/components/form';
import { Input } from '@workspace/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@workspace/ui/components/select';
import { Textarea } from '@workspace/ui/components/textarea';
import { toast } from '@workspace/ui/components/sonner';

import { createSubscription } from '~/actions/subscriptions/create-subscription';
import { updateSubscription } from '~/actions/subscriptions/update-subscription';
import ContractorModal from '~/components/contractors/contractor-modal';
import { useZodForm } from '~/hooks/use-zod-form';
import { createSubscriptionSchema, supportedCurrencies } from '~/schemas/subscriptions/create-subscription-schema';
import { updateSubscriptionSchema } from '~/schemas/subscriptions/update-subscription-schema';
import type { CategoryDto } from '~/types/dtos/category-dto';
import type { ContractorDto } from '~/types/dtos/contractor-dto';
import type { SubscriptionDto } from '~/types/dtos/subscription-dto';

export type SubscriptionFormProps = {
  subscription?: SubscriptionDto;
  categories: CategoryDto[];
  contractors?: ContractorDto[];
  onSuccess?: () => void;
  onCancel?: () => void;
};

export function SubscriptionForm({
  subscription,
  categories,
  contractors = [],
  onSuccess,
  onCancel
}: SubscriptionFormProps): React.JSX.Element {
  const router = useRouter();
  const isEditing = !!subscription;

  // Prepare default values based on editing state with proper typing
  const defaultValues = React.useMemo(() => {
    if (isEditing && subscription) {
      return {
        id: subscription.id,
        name: subscription.name,
        description: subscription.description ?? '',
        company: subscription.company ?? '',
        companyNip: subscription.companyNip ?? '',
        companyAddress: subscription.companyAddress ?? '',
        contractorId: subscription.contractorId ?? "none",
        type: subscription.type as 'income' | 'expense',
        netAmount: subscription.netAmount,
        vatRate: subscription.vatRate,
        currency: subscription.currency as 'PLN' | 'EUR' | 'USD' | 'GBP' | 'CHF',
        frequency: subscription.frequency as 'monthly' | 'quarterly' | 'semi_annual' | 'annual',
        billingDay: subscription.billingDay,
        endDate: subscription.endDate ?? null,
        status: subscription.status as 'active' | 'ended' | 'suspended' | 'trial',
        categoryId: subscription.categoryId ?? "none",
        isTaxDeductible: subscription.isTaxDeductible
      };
    }

    return {
      name: '',
      description: '',
      company: '',
      companyNip: '',
      companyAddress: '',
      contractorId: "none",
      type: 'expense' as const, // Default to expense (outcome)
      netAmount: 0,
      vatRate: 23,
      currency: 'PLN' as const, // Default to PLN
      frequency: 'monthly' as const, // Default to monthly
      billingDay: 1,
      // Removed endDate
      categoryId: "none",
      isTaxDeductible: false,
      status: 'active' as const // Default to active status
    };
  }, [isEditing, subscription]);

  const methods = useZodForm({
    schema: isEditing ? updateSubscriptionSchema : createSubscriptionSchema,
    mode: 'onBlur', // Only validate fields after they've been blurred
    criteriaMode: 'all', // Show all validation errors
    defaultValues
  });

  // Clear initial validation errors for select fields
  React.useEffect(() => {
    methods.clearErrors(['type', 'frequency']);
  }, [methods]);

  // Handle form submission with proper typing
  const onSubmit = methods.handleSubmit(async (formData: Record<string, any>) => {
    try {
      console.log('Form submission started with data:', formData);

      // Validate required fields manually to ensure they're present
      if (!formData.name || !formData.type || !formData.frequency ||
          formData.netAmount === undefined || formData.vatRate === undefined) {
        console.error('Missing required fields:', {
          name: formData.name,
          type: formData.type,
          frequency: formData.frequency,
          netAmount: formData.netAmount,
          vatRate: formData.vatRate
        });
        toast.error('Please fill in all required fields');
        return;
      }

      // Calculate values
      const netAmount = Number(formData.netAmount);
      const vatRate = Number(formData.vatRate);
      const vatAmount = (netAmount * vatRate) / 100;
      const grossAmount = netAmount + vatAmount;

      // Ensure all fields are properly typed and converted
      const processedData = {
        name: String(formData.name),
        description: formData.description || null,
        company: formData.company || null,
        contractorId: formData.contractorId === "none" ? null : formData.contractorId,
        type: formData.type as 'income' | 'expense',
        netAmount: netAmount,
        vatRate: vatRate,
        vatAmount: vatAmount,
        grossAmount: grossAmount,
        currency: formData.currency as 'PLN' | 'EUR' | 'USD' | 'GBP' | 'CHF',
        frequency: formData.frequency as 'monthly' | 'quarterly' | 'semi_annual' | 'annual',
        billingDay: Number(formData.billingDay),
        categoryId: formData.categoryId === "none" ? null : formData.categoryId,
        isTaxDeductible: Boolean(formData.isTaxDeductible)
      };

      console.log('Processed form data:', processedData);
      let success = false;

      if (isEditing) {
        // For update, we need the ID
        try {
          const updateData = {
            id: subscription!.id, // We know this exists because isEditing is true
            ...processedData
          };
          console.log('Updating subscription with ID:', updateData.id);
          const result = await updateSubscription(updateData);
          console.log('Update result:', result);
          toast.success('Subscription updated successfully');
          success = true;
        } catch (updateError) {
          console.error('Error updating subscription:', updateError);
          toast.error('Failed to update subscription');
        }
      } else {
        // For create, we don't need the ID
        try {
          console.log('Creating new subscription');
          const result = await createSubscription(processedData);
          console.log('Create result:', result);
          toast.success('Subscription created successfully');
          success = true;
        } catch (createError) {
          console.error('Error creating subscription:', createError);
          toast.error('Failed to create subscription');
        }
      }

      // Only call onSuccess if the operation was successful
      if (success) {
        console.log('Operation successful, calling onSuccess callback');

        // Always call onSuccess if available to close the modal
        if (onSuccess) {
          // Use a short timeout to ensure the server has time to process the request
          setTimeout(() => {
            onSuccess();
          }, 200);
        } else {
          // If no onSuccess callback, try to close the modal directly
          try {
            // Try to get the NiceModal instance
            const modalId = 'subscription-modal';
            const modal = NiceModal.getModal(modalId);

            if (modal) {
              console.log('Found modal instance, hiding it');
              NiceModal.hide(modalId);
              setTimeout(() => {
                try {
                  NiceModal.remove(modalId);
                } catch (e) {
                  console.error('Error removing modal:', e);
                }
                // Refresh the page to show the updated data
                window.location.reload();
              }, 100);
            } else {
              // If modal not found, just refresh the page
              console.log('Modal not found, refreshing page');
              router.refresh();
            }
          } catch (error) {
            console.error('Error closing modal:', error);
            // Fallback to page refresh
            router.refresh();
          }
        }
      }
    } catch (error) {
      console.error('Error saving subscription:', error);
      toast.error('Failed to save subscription');
    }
  });

  return (
    <FormProvider {...methods}>
      <form onSubmit={onSubmit} noValidate className="space-y-6">
        <FormField
          control={methods.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={methods.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea {...field} value={field.value || ''} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-2">
          <FormField
            control={methods.control}
            name="contractorId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Contractor</FormLabel>
                <div className="flex space-x-2">
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);

                      // If a contractor is selected, populate company fields
                      if (value !== "none") {
                        const selectedContractor = contractors.find(c => c.id === value);
                        if (selectedContractor) {
                          // Create address string
                          const addressParts = [];
                          if (selectedContractor.street) addressParts.push(selectedContractor.street);
                          if (selectedContractor.postalCode || selectedContractor.city) {
                            const cityPart = [selectedContractor.postalCode, selectedContractor.city].filter(Boolean).join(' ');
                            if (cityPart) addressParts.push(cityPart);
                          }
                          if (selectedContractor.country) addressParts.push(selectedContractor.country);
                          const addressString = addressParts.join(', ');

                          // Update form fields
                          methods.setValue('company', selectedContractor.shortName || selectedContractor.fullName);
                          methods.setValue('companyNip', selectedContractor.nip || '');
                          methods.setValue('companyAddress', addressString);
                        }
                      } else {
                        // Clear company fields if "None" is selected
                        methods.setValue('company', '');
                        methods.setValue('companyNip', '');
                        methods.setValue('companyAddress', '');
                      }
                    }}
                    defaultValue={field.value || "none"}
                    value={field.value || "none"}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select contractor" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {contractors.map((contractor) => (
                        <SelectItem key={contractor.id} value={contractor.id}>
                          {contractor.shortName || contractor.fullName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      // Register the modal
                      try {
                        NiceModal.remove('contractor-modal');
                      } catch (_) {
                        // Ignore errors
                      }
                      NiceModal.register('contractor-modal', ContractorModal);

                      // Show the modal
                      NiceModal.show('contractor-modal', {
                        onSuccess: (newContractor?: ContractorDto) => {
                          if (newContractor) {
                            // Set the newly created contractor as selected
                            methods.setValue('contractorId', newContractor.id);
                            // Trigger form validation
                            methods.trigger('contractorId');
                          } else {
                            // Fallback: refresh the page to get updated contractors list
                            window.location.reload();
                          }
                        }
                      });
                    }}
                  >
                    <PlusCircleIcon className="h-4 w-4" />
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={methods.control}
            name="company"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company Name (Display)</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={methods.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Type</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    // Clear error when value is selected
                    methods.clearErrors('type');
                  }}
                  value={field.value}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value={SubscriptionType.INCOME}>Income</SelectItem>
                    <SelectItem value={SubscriptionType.EXPENSE}>Expense</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={methods.control}
            name="categoryId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value || "none"}
                  value={field.value || "none"}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <FormField
            control={methods.control}
            name="netAmount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Net Amount</FormLabel>
                <FormControl>
                  <Input type="number" step="0.01" min="0" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={methods.control}
            name="vatRate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>VAT Rate (%)</FormLabel>
                <FormControl>
                  <Input type="number" step="0.01" min="0" max="100" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={methods.control}
            name="currency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Currency</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {supportedCurrencies.map((currency) => (
                      <SelectItem key={currency} value={currency}>
                        {currency}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={methods.control}
            name="frequency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Frequency</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(value);
                    // Clear error when value is selected
                    methods.clearErrors('frequency');
                  }}
                  value={field.value}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value={SubscriptionFrequency.MONTHLY}>Monthly</SelectItem>
                    <SelectItem value={SubscriptionFrequency.QUARTERLY}>Quarterly</SelectItem>
                    <SelectItem value={SubscriptionFrequency.SEMI_ANNUAL}>Semi-Annual</SelectItem>
                    <SelectItem value={SubscriptionFrequency.ANNUAL}>Annual</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={methods.control}
            name="billingDay"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Billing Day</FormLabel>
                <FormControl>
                  <Input type="number" min="1" max="31" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* Display calculated VAT amount */}
          <div>
            <FormLabel>VAT Amount</FormLabel>
            <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background">
              {(() => {
                const netAmount = Number(methods.watch('netAmount') || 0);
                const vatRate = Number(methods.watch('vatRate') || 0);
                const vatAmount = (netAmount * vatRate) / 100;
                return `${vatAmount.toFixed(2)} ${methods.watch('currency') || 'PLN'}`;
              })()}
            </div>
            <p className="text-xs text-muted-foreground">Calculated based on net amount and VAT rate</p>
          </div>

          {/* Display calculated gross amount */}
          <div>
            <FormLabel>Gross Amount</FormLabel>
            <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background">
              {(() => {
                const netAmount = Number(methods.watch('netAmount') || 0);
                const vatRate = Number(methods.watch('vatRate') || 0);
                const vatAmount = (netAmount * vatRate) / 100;
                const grossAmount = netAmount + vatAmount;
                return `${grossAmount.toFixed(2)} ${methods.watch('currency') || 'PLN'}`;
              })()}
            </div>
            <p className="text-xs text-muted-foreground">Net amount + VAT amount</p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {isEditing && (
            <FormField
              control={methods.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={SubscriptionStatus.ACTIVE}>Active</SelectItem>
                      <SelectItem value={SubscriptionStatus.ENDED}>Ended</SelectItem>
                      <SelectItem value={SubscriptionStatus.SUSPENDED}>Suspended</SelectItem>
                      <SelectItem value={SubscriptionStatus.TRIAL}>Trial</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        <FormField
          control={methods.control}
          name="isTaxDeductible"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Tax Deductible</FormLabel>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              console.log('Cancel button clicked');

              // Use the onCancel prop if available
              if (onCancel) {
                console.log('Using onCancel prop');
                onCancel();
                return;
              }

              // Try to find the modal instance and close it directly
              try {
                // Try to get the NiceModal instance
                const modalId = 'subscription-modal';
                const modal = NiceModal.getModal(modalId);

                if (modal) {
                  console.log('Found modal instance, hiding it');
                  NiceModal.hide(modalId);
                  setTimeout(() => {
                    try {
                      NiceModal.remove(modalId);
                    } catch (e) {
                      console.error('Error removing modal:', e);
                    }
                  }, 100);
                  return;
                }

                // Fallback to DOM manipulation if NiceModal instance not found
                console.log('Modal instance not found, trying DOM manipulation');
                const modalElement = window.document.querySelector('[data-radix-dialog-content]') ||
                                    window.document.querySelector('[data-vaul-drawer-content]');

                // Try to find and click the close button
                if (modalElement) {
                  const closeButton = modalElement.querySelector('button[aria-label="Close"]') ||
                                     modalElement.querySelector('button.close') ||
                                     modalElement.querySelector('button[class*="Close"]');
                  if (closeButton) {
                    console.log('Found close button, clicking it');
                    // Cast to HTMLElement to access click method
                    (closeButton as HTMLElement).click();
                    return;
                  }
                }

                // If we're here, we couldn't find a way to close the modal
                // Just navigate back to the subscriptions list
                const currentPath = window.location.pathname;
                const orgSlugMatch = currentPath.match(/\/organizations\/([^/]+)/);
                if (orgSlugMatch && orgSlugMatch[1]) {
                  const orgSlug = orgSlugMatch[1];
                  window.location.href = `/organizations/${orgSlug}/subscriptions`;
                } else {
                  // Final fallback - refresh the page
                  window.location.reload();
                }
              } catch (error) {
                console.error('Error in cancel button handler:', error);
                // Final fallback - refresh the page
                window.location.reload();
              }
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={methods.formState.isSubmitting}
          >
            {methods.formState.isSubmitting ? 'Saving...' : (isEditing ? 'Update' : 'Create')} Subscription
          </Button>
        </div>
      </form>
    </FormProvider>
  );
}
