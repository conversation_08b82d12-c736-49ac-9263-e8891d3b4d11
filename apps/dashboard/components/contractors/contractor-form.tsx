'use client';

import * as React from 'react';
import { useRouter } from 'next/navigation';

import { Button } from '@workspace/ui/components/button';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormProvider
} from '@workspace/ui/components/form';
import { Input } from '@workspace/ui/components/input';
import { toast } from '@workspace/ui/components/sonner';

import { NipInput } from '~/components/nip-input';
import { mapCompanyDataToFormFields } from '~/hooks/use-nip-validator';
import { type CompanyData } from '~/lib/nip-validator';

import { createContractor } from '~/actions/contractors/create-contractor';
import { updateContractor } from '~/actions/contractors/update-contractor';
import { useZodForm } from '~/hooks/use-zod-form';
import { createContractorSchema } from '~/schemas/contractors/create-contractor-schema';
import { updateContractorSchema } from '~/schemas/contractors/update-contractor-schema';
import type { ContractorDto } from '~/types/dtos/contractor-dto';

export type ContractorFormProps = {
  contractor?: ContractorDto;
  onSuccess?: (newContractor?: ContractorDto) => void;
  onCancel?: () => void;
};

export function ContractorForm({
  contractor,
  onSuccess,
  onCancel
}: ContractorFormProps): React.JSX.Element {
  const router = useRouter();
  const isEditing = !!contractor;

  // Set up default values based on whether we're editing or creating
  const defaultValues = React.useMemo(() => {
    if (isEditing && contractor) {
      return {
        id: contractor.id,
        shortName: contractor.shortName || '',
        fullName: contractor.fullName,
        nip: contractor.nip || '',
        regon: contractor.regon || '',
        country: contractor.country || '',
        postalCode: contractor.postalCode || '',
        city: contractor.city || '',
        street: contractor.street || '',
        email: contractor.email || '',
        phone: contractor.phone || ''
      };
    }

    return {
      shortName: '',
      fullName: '',
      nip: '',
      regon: '',
      country: '',
      postalCode: '',
      city: '',
      street: '',
      email: '',
      phone: ''
    };
  }, [isEditing, contractor]);

  const methods = useZodForm({
    schema: isEditing ? updateContractorSchema : createContractorSchema,
    mode: 'onBlur', // Only validate fields after they've been blurred
    criteriaMode: 'all', // Show all validation errors
    defaultValues
  });

  // Handle form submission
  const onSubmit = methods.handleSubmit(async (formData) => {
    try {
      console.log('Form submission started with data:', formData);

      // Validate required fields manually to ensure they're present
      if (!formData.fullName) {
        console.error('Missing required fields:', {
          fullName: formData.fullName
        });
        toast.error('Please fill in all required fields');
        return;
      }

      let createdOrUpdatedContractor: ContractorDto | undefined;

      if (isEditing) {
        // Update existing contractor
        const result = await updateContractor({
          id: contractor!.id,
          ...formData
        });
        console.log('Contractor updated:', result);
        toast.success('Contractor updated successfully');
        createdOrUpdatedContractor = result.data;
      } else {
        // Create new contractor
        const result = await createContractor(formData);
        console.log('Contractor created:', result);
        toast.success('Contractor created successfully');
        createdOrUpdatedContractor = result.data;
      }

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(createdOrUpdatedContractor);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Error saving contractor');
    }
  });

  return (
    <FormProvider {...methods}>
      <form onSubmit={onSubmit} noValidate className="space-y-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <NipInput
            name="nip"
            control={methods.control}
            onCompanyDataFetched={(data: CompanyData) => {
              // Map company data to form fields
              const formData = mapCompanyDataToFormFields(data);

              // Update form fields with the fetched data
              methods.setValue('fullName', formData.fullName);
              methods.setValue('regon', formData.regon);
              methods.setValue('country', formData.country || '');
              methods.setValue('postalCode', formData.postalCode);
              methods.setValue('city', formData.city);
              methods.setValue('street', formData.street);
            }}
            disabled={methods.formState.isSubmitting}
          />

          <FormField
            control={methods.control}
            name="regon"
            render={({ field }) => (
              <FormItem>
                <FormLabel>REGON</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={methods.control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Pełna nazwa</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={methods.control}
            name="shortName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Skrócona nazwa</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={methods.control}
            name="country"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Kraj</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={methods.control}
            name="postalCode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Kod pocztowy</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={methods.control}
            name="city"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Miasto</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={methods.control}
            name="street"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Ulica i numer</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <FormField
            control={methods.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} type="email" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={methods.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Telefon</FormLabel>
                <FormControl>
                  <Input {...field} value={field.value || ''} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              Anuluj
            </Button>
          )}
          <Button type="submit" disabled={methods.formState.isSubmitting}>
            {isEditing ? 'Zapisz zmiany' : 'Dodaj kontrahenta'}
          </Button>
        </div>
      </form>
    </FormProvider>
  );
}
