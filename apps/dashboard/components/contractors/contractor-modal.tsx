'use client';

import * as React from 'react';
import NiceModal, { useModal, type NiceModalHocProps } from '@ebay/nice-modal-react';
import { useMediaQuery } from '@workspace/ui/hooks/use-media-query';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@workspace/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle
} from '@workspace/ui/components/drawer';

import { ContractorForm } from '~/components/contractors/contractor-form';
import { MediaQueries } from '@workspace/ui/lib/media-queries';
import type { ContractorDto } from '~/types/dtos/contractor-dto';

export type ContractorModalProps = NiceModalHocProps & {
  contractor?: ContractorDto;
  onSuccess?: (newContractor?: ContractorDto) => void;
};

function ContractorModal({
  contractor,
  onSuccess
}: ContractorModalProps): React.JSX.Element {
  const modal = useModal();
  const mdUp = useMediaQuery(MediaQueries.MdUp);

  const isEditing = !!contractor;
  const title = isEditing ? 'Edytuj kontrahenta' : 'Dodaj kontrahenta';

  const handleCloseModal = React.useCallback(() => {
    modal.remove();
  }, [modal]);

  const handleOpenChange = React.useCallback((open: boolean) => {
    if (!open) {
      modal.remove();
    }
  }, [modal]);

  const renderContent = (
    <ContractorForm
      contractor={contractor}
      onSuccess={(newContractor) => {
        if (onSuccess) {
          onSuccess(newContractor);
        }
        modal.remove();
      }}
      onCancel={handleCloseModal}
    />
  );

  if (mdUp) {
    return (
      <Dialog
        open={modal.visible}
        onOpenChange={handleOpenChange}
      >
        <DialogContent
          className="sm:max-w-[600px] max-h-[90vh]"
          onClose={handleCloseModal}
        >
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
          <div className="px-6 py-4 overflow-y-auto max-h-[calc(90vh-120px)]">{renderContent}</div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer
      open={modal.visible}
      onOpenChange={handleOpenChange}
    >
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>{title}</DrawerTitle>
        </DrawerHeader>
        <div className="px-4 py-4 overflow-y-auto max-h-[calc(100vh-120px)]">{renderContent}</div>
      </DrawerContent>
    </Drawer>
  );
}

export default NiceModal.create(ContractorModal);
