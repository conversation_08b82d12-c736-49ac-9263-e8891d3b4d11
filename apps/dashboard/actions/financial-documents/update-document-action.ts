'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import {
  updateFinancialDocument,
  DocumentType,
  PaymentMethod,
  DocumentStatus,
  PaymentStatus,
  SourceType,
  DocumentExpenseStatus,
  BusinessExpenseStatus
} from '@workspace/database/queries';
import { replaceOrgSlug, routes } from '@workspace/routes';
import { authOrganizationActionClient } from '~/actions/safe-action';

// Schema for line items
const lineItemSchema = z.object({
  id: z.string().uuid().optional(),
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().positive('Quantity must be positive'),
  unitPrice: z.number().nonnegative('Unit price must be non-negative'),
  netAmount: z.number().nonnegative('Net amount must be non-negative'),
  vatRate: z.number().nonnegative('VAT rate must be non-negative'),
  vatAmount: z.number().nonnegative('VAT amount must be non-negative'),
  grossAmount: z.number().nonnegative('Gross amount must be non-negative'),
  isBusinessExpense: z.nativeEnum(BusinessExpenseStatus).optional(),
});

// Schema for updating a financial document
const updateDocumentSchema = z.object({
  id: z.string().uuid(),
  documentType: z.nativeEnum(DocumentType).optional(),
  invoiceNumber: z.string().min(1, 'Invoice number is required').optional(),
  issueDate: z.date().optional(),
  dueDate: z.date().optional(),
  saleDate: z.date().optional(),
  netAmount: z.number().nonnegative('Net amount must be non-negative').optional(),
  vatAmount: z.number().nonnegative('VAT amount must be non-negative').optional(),
  vatPercentage: z.number().nonnegative('VAT percentage must be non-negative').optional(),
  grossAmount: z.number().nonnegative('Gross amount must be non-negative').optional(),
  currency: z.string().length(3, 'Currency must be a 3-letter code').optional(),
  plnAmount: z.number().nonnegative('PLN amount must be non-negative').optional(),
  clientName: z.string().min(1, 'Client name is required').optional(),
  clientVatId: z.string().optional().nullable(),
  contractorId: z.string().uuid().optional().nullable(),
  paymentMethod: z.nativeEnum(PaymentMethod).optional(),
  paymentAccountNumber: z.string().optional().nullable(),
  paymentDeadlineExceeded: z.boolean().optional(),
  categoryId: z.string().uuid().optional().nullable(),
  isCompanyExpense: z.nativeEnum(DocumentExpenseStatus).optional(),
  status: z.nativeEnum(DocumentStatus).optional(),
  paymentStatus: z.nativeEnum(PaymentStatus).optional(),
  documentPdfUrl: z.string().url().optional().nullable(),
  sourceType: z.nativeEnum(SourceType).optional(),
  sourceDetails: z.string().optional().nullable(),
  transactionId: z.string().uuid().optional().nullable(),
  subscriptionId: z.string().uuid().optional().nullable(),
  frequency: z.string().optional().nullable(),
  lineItems: z.array(lineItemSchema).optional(),
});

/**
 * Server action for updating a financial document
 */
export const updateDocumentAction = authOrganizationActionClient
  .metadata({ actionName: 'updateDocumentAction' })
  .schema(updateDocumentSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const { id, ...data } = parsedInput;

      // Verify the document belongs to the organization
      const document = await updateFinancialDocument(id, data);

      if (!document) {
        return {
          success: false,
          error: 'Document not found or does not belong to this organization'
        };
      }

      // Revalidate the financial documents page and the single document page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.Invoices,
          ctx.organization.slug
        )
      );

      // Revalidate the single document page
      const singleDocumentPath = replaceOrgSlug(
        routes.dashboard.organizations.slug.invoices.Id,
        ctx.organization.slug
      ).replace('[id]', id);

      revalidatePath(singleDocumentPath);

      return { success: true, document };
    } catch (error) {
      console.error('Error updating financial document:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update financial document'
      };
    }
  });
