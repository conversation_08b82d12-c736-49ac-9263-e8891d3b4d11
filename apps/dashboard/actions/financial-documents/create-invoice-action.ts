'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import {
  DocumentType,
  DocumentStatus,
  PaymentStatus,
  PaymentMethod,
  SourceType,
  DocumentExpenseStatus,
  createFinancialDocument,
  createFinancialDocumentLineItem,
  createContractor,
  getNextInvoiceNumber
} from '@workspace/database/queries';
import { authOrganizationActionClient } from '~/actions/safe-action';
import { createInvoiceSchema } from '~/schemas/financial-documents/create-invoice-schema';
import { validatePolishInvoiceCompliance } from '~/lib/invoice-utils';
import { prisma } from '@workspace/database/client';

// Server action for creating an invoice
export const createInvoiceAction = authOrganizationActionClient
  .metadata({ actionName: 'createInvoiceAction' })
  .schema(createInvoiceSchema)
  .action(async ({ parsedInput: data, ctx }) => {
    try {
      console.log('Creating invoice with data:', data);

      const { organization } = ctx;

      // Get the last invoice number for Polish compliance validation
      const lastInvoice = await prisma.financialDocument.findFirst({
        where: {
          organizationId: organization.id,
          documentType: DocumentType.INVOICE,
          status: { not: DocumentStatus.DRAFT } // Only count issued invoices
        },
        orderBy: [
          { issueDate: 'desc' },
          { invoiceNumber: 'desc' }
        ],
        select: {
          invoiceNumber: true,
          issueDate: true
        }
      });

      // Validate Polish invoice compliance
      const complianceValidation = validatePolishInvoiceCompliance(
        data.invoiceNumber,
        data.issueDate,
        data.saleDate,
        lastInvoice?.invoiceNumber || null
      );

      if (!complianceValidation.isValid) {
        return {
          success: false,
          error: complianceValidation.error || 'Faktura nie spełnia wymogów polskiego prawa VAT'
        };
      }

      // Check if invoice number already exists (database-level uniqueness)
      const existingInvoice = await prisma.financialDocument.findFirst({
        where: {
          organizationId: organization.id,
          invoiceNumber: data.invoiceNumber,
          documentType: DocumentType.INVOICE
        },
        select: {
          id: true,
          invoiceNumber: true
        }
      });

      if (existingInvoice) {
        return {
          success: false,
          error: `Faktura o numerze ${data.invoiceNumber} już istnieje. Zgodnie z polskim prawem VAT numery faktur muszą być unikalne.`
        };
      }

      let contractorId = data.contractorId;

      // If a new contractor is provided, create it first
      if (data.newContractor && !contractorId) {
        console.log('Creating new contractor:', data.newContractor);

        const newContractor = await createContractor({
          organizationId: organization.id,
          shortName: data.newContractor.companyName.substring(0, 50), // Truncate if too long
          fullName: data.newContractor.companyName,
          nip: data.newContractor.nip || null,
          email: data.newContractor.email || null,
          street: data.newContractor.street,
          city: data.newContractor.city,
          postalCode: data.newContractor.postalCode,
          country: data.newContractor.country
        });

        contractorId = newContractor.id;
        console.log('New contractor created with ID:', contractorId);
      }

      // Calculate PLN amount (for now, assume same as gross amount if PLN, otherwise would need exchange rate)
      const plnAmount = data.currency === 'PLN' ? data.grossAmount : data.grossAmount;

      // Get client name - either from new contractor or existing contractor
      let clientName = 'Unknown Client';
      let clientVatId = null;

      if (data.newContractor) {
        clientName = data.newContractor.companyName;
        clientVatId = data.newContractor.nip || null;
      } else if (contractorId) {
        // TODO: Fetch contractor details to get name and NIP
        // For now, we'll use a placeholder
        clientName = 'Existing Contractor';
      }

      // Create the financial document
      const documentData = {
        organizationId: organization.id,
        documentType: DocumentType.INVOICE,
        invoiceNumber: data.invoiceNumber,
        issueDate: data.issueDate,
        dueDate: data.dueDate || new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // Default 14 days
        saleDate: data.saleDate,
        netAmount: data.netAmount,
        vatAmount: data.vatAmount,
        vatPercentage: data.vatAmount > 0 ? (data.vatAmount / data.netAmount) * 100 : 0,
        grossAmount: data.grossAmount,
        currency: data.currency,
        plnAmount: plnAmount,
        clientName: clientName,
        clientVatId: clientVatId,
        contractorId: contractorId,
        paymentMethod: data.paymentMethod as PaymentMethod,
        paymentAccountNumber: data.paymentAccountNumber || null,
        paymentDeadlineExceeded: false,
        categoryId: data.categoryId || null,
        isCompanyExpense: DocumentExpenseStatus.NEED_VERIFICATION,
        status: data.status as DocumentStatus,
        paymentStatus: PaymentStatus.UNPAID,
        documentPdfUrl: null, // Will be generated later
        sourceType: SourceType.MANUAL,
        sourceDetails: 'Created via invoice form'
      };

      console.log('Creating financial document with data:', documentData);
      const document = await createFinancialDocument(documentData);
      console.log('Financial document created:', document);

      // Create line items
      for (const lineItem of data.lineItems) {
        const lineItemData = {
          documentId: document.id,
          description: lineItem.description,
          quantity: lineItem.quantity,
          unitPrice: lineItem.netPrice,
          netAmount: lineItem.netAmount,
          vatRate: lineItem.vatRate,
          vatAmount: lineItem.vatAmount,
          grossAmount: lineItem.grossAmount
        };

        console.log('Creating line item:', lineItemData);
        await createFinancialDocumentLineItem(lineItemData);
      }

      // Generate PDF invoice automatically for issued invoices
      if (data.status !== DocumentStatus.DRAFT) {
        try {
          const { generateInvoicePdfAction } = await import('./generate-pdf-action');
          const pdfResult = await generateInvoicePdfAction({ invoiceId: document.id });

          if (!pdfResult?.success) {
            console.warn('Failed to generate PDF for invoice:', pdfResult?.error);
          }
        } catch (error) {
          console.warn('Error generating PDF for invoice:', error);
          // Don't fail the invoice creation if PDF generation fails
        }
      }

      // TODO: Send email if requested

      // Revalidate the invoices page
      revalidatePath(`/organizations/${organization.slug}/invoices/income`);
      revalidatePath(`/organizations/${organization.slug}/invoices`);

      return {
        success: true,
        data: {
          id: document.id,
          invoiceNumber: document.invoiceNumber,
          grossAmount: document.grossAmount,
          currency: document.currency
        }
      };

    } catch (error) {
      console.error('Error creating invoice:', error);

      // Return a user-friendly error message
      const errorMessage = error instanceof Error
        ? error.message
        : 'An unexpected error occurred while creating the invoice';

      return {
        success: false,
        error: errorMessage
      };
    }
  });

// Server action for saving invoice as draft
export const saveInvoiceDraftAction = authOrganizationActionClient
  .metadata({ actionName: 'saveInvoiceDraftAction' })
  .schema(createInvoiceSchema)
  .action(async ({ parsedInput: data, ctx }) => {
    try {
      console.log('Saving invoice draft with data:', data);

      const { organization } = ctx;

      // Check if invoice number already exists (excluding drafts for the same number)
      const existingInvoice = await prisma.financialDocument.findFirst({
        where: {
          organizationId: organization.id,
          invoiceNumber: data.invoiceNumber,
          documentType: DocumentType.INVOICE,
          status: { not: DocumentStatus.DRAFT } // Allow drafts with same number
        },
        select: {
          id: true,
          invoiceNumber: true
        }
      });

      if (existingInvoice) {
        return {
          success: false,
          error: `Faktura o numerze ${data.invoiceNumber} już istnieje. Proszę użyć innego numeru.`
        };
      }

      let contractorId = data.contractorId;

      // Create contractor if new contractor data is provided
      if (data.newContractor && !contractorId) {
        console.log('Creating new contractor for draft:', data.newContractor);
        const newContractor = await createContractor({
          organizationId: organization.id,
          fullName: data.newContractor.companyName,
          nip: data.newContractor.nip || null,
          street: data.newContractor.street,
          city: data.newContractor.city,
          postalCode: data.newContractor.postalCode,
          country: data.newContractor.country,
          email: data.newContractor.email || null,
        });
        contractorId = newContractor.id;
      }

      // Determine client information
      let clientName = '';
      let clientVatId = null;

      if (contractorId) {
        // Use existing contractor
        const contractor = await prisma.contractor.findUnique({
          where: { id: contractorId }
        });
        if (contractor) {
          clientName = contractor.fullName;
          clientVatId = contractor.nip;
        }
      } else if (data.newContractor) {
        // Use new contractor data
        clientName = data.newContractor.companyName;
        clientVatId = data.newContractor.nip || null;
      }

      // Calculate PLN amount (assuming PLN for now)
      const plnAmount = data.currency === 'PLN' ? data.grossAmount : data.grossAmount;

      // Create the financial document as draft
      const documentData = {
        organizationId: organization.id,
        documentType: DocumentType.INVOICE,
        invoiceNumber: data.invoiceNumber,
        issueDate: data.issueDate,
        dueDate: data.dueDate || new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // Default 14 days
        saleDate: data.saleDate,
        netAmount: data.netAmount,
        vatAmount: data.vatAmount,
        vatPercentage: data.vatAmount > 0 ? (data.vatAmount / data.netAmount) * 100 : 0,
        grossAmount: data.grossAmount,
        currency: data.currency,
        plnAmount: plnAmount,
        clientName: clientName,
        clientVatId: clientVatId,
        contractorId: contractorId,
        paymentMethod: data.paymentMethod as PaymentMethod,
        paymentAccountNumber: data.paymentAccountNumber || null,
        paymentDeadlineExceeded: false,
        categoryId: data.categoryId || null,
        isCompanyExpense: DocumentExpenseStatus.NEED_VERIFICATION,
        status: DocumentStatus.DRAFT, // Set as draft
        paymentStatus: PaymentStatus.UNPAID,
        sourceType: SourceType.MANUAL,
        sourceDetails: 'Created via invoice form as draft',
        transactionId: null,
        subscriptionId: null,
        frequency: null,
        lineItems: data.lineItems?.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.netPrice,
          netAmount: item.netAmount,
          vatRate: item.vatRate,
          vatAmount: item.vatAmount,
          grossAmount: item.grossAmount,
        })) || []
      };

      console.log('Creating draft document with data:', documentData);

      const document = await createFinancialDocument(documentData);

      console.log('Draft document created successfully:', document.id);

      // Revalidate the invoices page
      revalidatePath(`/organizations/${organization.slug}/invoices/income`);
      revalidatePath(`/organizations/${organization.slug}/invoices`);

      return {
        success: true,
        data: {
          id: document.id,
          invoiceNumber: document.invoiceNumber,
          grossAmount: document.grossAmount,
          currency: document.currency
        }
      };

    } catch (error) {
      console.error('Error saving invoice draft:', error);

      const errorMessage = error instanceof Error
        ? error.message
        : 'An unexpected error occurred while saving the draft';

      return {
        success: false,
        error: errorMessage
      };
    }
  });



// Server action for getting the next invoice number
export const getNextInvoiceNumberAction = authOrganizationActionClient
  .metadata({ actionName: 'getNextInvoiceNumberAction' })
  .action(async ({ ctx }) => {
    console.log('🔍 getNextInvoiceNumberAction called');

    // Always ensure we return a valid invoice number
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');

    console.log('📅 Date info:', { year, month, now: now.toISOString() });

    try {
      const { organization } = ctx;
      console.log('🏢 Organization:', { id: organization.id, name: organization.name });

      // Try the main function first
      console.log('📞 Calling getNextInvoiceNumber...');
      const nextInvoiceNumber = await getNextInvoiceNumber(organization.id);
      console.log('📋 Generated invoice number:', nextInvoiceNumber);

      // Validate that we got a proper invoice number
      if (nextInvoiceNumber && nextInvoiceNumber.match(/^FV\/\d{4}\/(0[1-9]|1[0-2])\/\d{2,}$/)) {
        console.log('✅ Valid invoice number, returning success');
        return {
          success: true,
          invoiceNumber: nextInvoiceNumber
        };
      }

      // If invalid, fall through to fallback
      console.warn(`⚠️ Invalid invoice number generated: ${nextInvoiceNumber}, using fallback`);
    } catch (error) {
      console.error('💥 Error in main try block:', error);
      console.error('💥 Error message:', error instanceof Error ? error.message : 'Unknown error');
      console.error('💥 Error stack:', error instanceof Error ? error.stack : 'No stack');
    }

    // Fallback logic - always executed if main function fails
    console.log('🔄 Entering fallback logic...');
    try {
      const { organization } = ctx;
      console.log('🔄 Fallback organization:', { id: organization.id });

      // Try to get the last invoice for this month to determine next sequential number
      console.log('🔍 Searching for last invoice with pattern:', `FV/${year}/${month}/`);
      const lastInvoice = await prisma.financialDocument.findFirst({
        where: {
          organizationId: organization.id,
          documentType: DocumentType.INVOICE,
          invoiceNumber: {
            startsWith: `FV/${year}/${month}/`,
          },
        },
        select: {
          invoiceNumber: true,
        },
        orderBy: {
          invoiceNumber: 'desc',
        },
      });

      console.log('📄 Last invoice found in fallback:', lastInvoice);

      let nextSequential = 1;
      if (lastInvoice) {
        const parts = lastInvoice.invoiceNumber.split('/');
        console.log('🔢 Invoice number parts:', parts);
        if (parts.length === 4) {
          const lastSequential = parseInt(parts[3], 10);
          if (!isNaN(lastSequential)) {
            nextSequential = lastSequential + 1;
            console.log('➕ Next sequential calculated:', nextSequential);
          }
        }
      } else {
        console.log('🆕 No existing invoices, starting with 1');
      }

      const fallbackNumber = `FV/${year}/${month}/${nextSequential.toString().padStart(2, '0')}`;
      console.log('🔄 Generated fallback number:', fallbackNumber);

      return {
        success: true,
        invoiceNumber: fallbackNumber
      };
    } catch (fallbackError) {
      console.error('💥 Fallback also failed:', fallbackError);
      console.error('💥 Fallback error message:', fallbackError instanceof Error ? fallbackError.message : 'Unknown fallback error');

      // Final fallback - always return a valid number
      const finalFallback = `FV/${year}/${month}/01`;
      console.log('🆘 Using final fallback:', finalFallback);

      return {
        success: true,
        invoiceNumber: finalFallback
      };
    }
  });

// Server action for checking if invoice number already exists
export const checkInvoiceNumberExistsAction = authOrganizationActionClient
  .metadata({ actionName: 'checkInvoiceNumberExistsAction' })
  .schema(z.object({
    invoiceNumber: z.string().min(1, 'Invoice number is required'),
    excludeId: z.string().optional() // For editing existing invoices
  }))
  .action(async ({ parsedInput, ctx }) => {
    try {
      const { organization } = ctx;
      const { invoiceNumber, excludeId } = parsedInput;

      const existingInvoice = await prisma.financialDocument.findFirst({
        where: {
          organizationId: organization.id,
          invoiceNumber,
          documentType: DocumentType.INVOICE,
          ...(excludeId && { id: { not: excludeId } })
        },
        select: {
          id: true,
          invoiceNumber: true
        }
      });

      return {
        success: true,
        exists: !!existingInvoice,
        existingInvoice: existingInvoice || null
      };
    } catch (error) {
      console.error('Error checking invoice number:', error);

      return {
        success: false,
        error: 'Failed to check invoice number'
      };
    }
  });

// Server action for getting the last issued invoice number
export const getLastInvoiceNumberAction = authOrganizationActionClient
  .metadata({ actionName: 'getLastInvoiceNumberAction' })
  .action(async ({ ctx }) => {
    try {
      const { organization } = ctx;

      const lastInvoice = await prisma.financialDocument.findFirst({
        where: {
          organizationId: organization.id,
          documentType: DocumentType.INVOICE,
          status: { not: DocumentStatus.DRAFT } // Only count issued invoices
        },
        orderBy: [
          { issueDate: 'desc' },
          { invoiceNumber: 'desc' }
        ],
        select: {
          invoiceNumber: true,
          issueDate: true
        }
      });

      return {
        success: true,
        lastInvoice: lastInvoice || null
      };
    } catch (error) {
      console.error('Error getting last invoice number:', error);

      return {
        success: false,
        error: 'Failed to get last invoice number'
      };
    }
  });