'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { 
  createFinancialDocument, 
  DocumentType, 
  PaymentMethod, 
  DocumentStatus, 
  PaymentStatus, 
  SourceType, 
  DocumentExpenseStatus, 
  BusinessExpenseStatus 
} from '@workspace/database/queries';
import { replaceOrgSlug, routes } from '@workspace/routes';
import { authOrganizationActionClient } from '~/actions/safe-action';

// Schema for line items
const lineItemSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  quantity: z.number().positive('Quantity must be positive'),
  unitPrice: z.number().nonnegative('Unit price must be non-negative'),
  netAmount: z.number().nonnegative('Net amount must be non-negative'),
  vatRate: z.number().nonnegative('VAT rate must be non-negative'),
  vatAmount: z.number().nonnegative('VAT amount must be non-negative'),
  grossAmount: z.number().nonnegative('Gross amount must be non-negative'),
  isBusinessExpense: z.nativeEnum(BusinessExpenseStatus).optional(),
});

// Schema for creating a financial document
const createDocumentSchema = z.object({
  documentType: z.nativeEnum(DocumentType),
  invoiceNumber: z.string().min(1, 'Invoice number is required'),
  issueDate: z.date(),
  dueDate: z.date(),
  saleDate: z.date(),
  netAmount: z.number().nonnegative('Net amount must be non-negative'),
  vatAmount: z.number().nonnegative('VAT amount must be non-negative'),
  vatPercentage: z.number().nonnegative('VAT percentage must be non-negative'),
  grossAmount: z.number().nonnegative('Gross amount must be non-negative'),
  currency: z.string().length(3, 'Currency must be a 3-letter code'),
  plnAmount: z.number().nonnegative('PLN amount must be non-negative'),
  clientName: z.string().min(1, 'Client name is required'),
  clientVatId: z.string().optional().nullable(),
  contractorId: z.string().uuid().optional().nullable(),
  paymentMethod: z.nativeEnum(PaymentMethod),
  paymentAccountNumber: z.string().optional().nullable(),
  paymentDeadlineExceeded: z.boolean().optional(),
  categoryId: z.string().uuid().optional().nullable(),
  isCompanyExpense: z.nativeEnum(DocumentExpenseStatus).optional(),
  status: z.nativeEnum(DocumentStatus).optional(),
  paymentStatus: z.nativeEnum(PaymentStatus).optional(),
  documentPdfUrl: z.string().url().optional().nullable(),
  sourceType: z.nativeEnum(SourceType).optional(),
  sourceDetails: z.string().optional().nullable(),
  transactionId: z.string().uuid().optional().nullable(),
  subscriptionId: z.string().uuid().optional().nullable(),
  frequency: z.string().optional().nullable(),
  lineItems: z.array(lineItemSchema).optional(),
});

/**
 * Server action for creating a financial document
 */
export const createDocumentAction = authOrganizationActionClient
  .metadata({ actionName: 'createDocumentAction' })
  .schema(createDocumentSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const document = await createFinancialDocument({
        organizationId: ctx.organization.id,
        ...parsedInput,
      });

      // Revalidate the financial documents page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.Invoices,
          ctx.organization.slug
        )
      );

      return { success: true, document };
    } catch (error) {
      console.error('Error creating financial document:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to create financial document' 
      };
    }
  });
