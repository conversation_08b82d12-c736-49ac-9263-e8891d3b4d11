'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { deleteFinancialDocument, getFinancialDocument } from '@workspace/database/queries';
import { replaceOrgSlug, routes } from '@workspace/routes';
import { authOrganizationActionClient } from '~/actions/safe-action';

// Schema for deleting a financial document
const deleteDocumentSchema = z.object({
  id: z.string().uuid(),
});

/**
 * Server action for deleting a financial document
 */
export const deleteDocumentAction = authOrganizationActionClient
  .metadata({ actionName: 'deleteDocumentAction' })
  .schema(deleteDocumentSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const { id } = parsedInput;

      // Verify the document belongs to the organization
      const document = await getFinancialDocument(id, ctx.organization.id);

      if (!document) {
        return { 
          success: false, 
          error: 'Document not found or does not belong to this organization' 
        };
      }

      // Delete the document
      await deleteFinancialDocument(id);

      // Revalidate the financial documents page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.Invoices,
          ctx.organization.slug
        )
      );

      return { success: true };
    } catch (error) {
      console.error('Error deleting financial document:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to delete financial document' 
      };
    }
  });
