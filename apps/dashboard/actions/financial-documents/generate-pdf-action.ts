'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { prisma } from '@workspace/database/client';
import { authOrganizationActionClient } from '~/actions/safe-action';
import { InvoicePDFGenerator } from '~/lib/pdf-generator';
import { fileStorageService } from '~/lib/file-storage-service';
import { getOrganizationDetails } from '~/data/organization/get-organization-details';
import { getOrganizationLogo } from '~/data/organization/get-organization-logo';

const generatePdfSchema = z.object({
  invoiceId: z.string().uuid('Invalid invoice ID')
});

export const generateInvoicePdfAction = authOrganizationActionClient
  .metadata({ actionName: 'generateInvoicePdfAction' })
  .schema(generatePdfSchema)
  .action(async ({ parsedInput: { invoiceId }, ctx }) => {
    try {
      const { organization } = ctx;

      // Get the invoice data
      const invoice = await prisma.financialDocument.findFirst({
        where: {
          id: invoiceId,
          organizationId: organization.id
        },
        include: {
          lineItems: true,
          contractor: true
        }
      });

      if (!invoice) {
        return {
          success: false,
          error: 'Invoice not found'
        };
      }

      // Get organization details and logo
      const [orgDetails, orgLogo] = await Promise.all([
        getOrganizationDetails(),
        getOrganizationLogo()
      ]);

      // Prepare PDF data
      const pdfData = {
        // Invoice basic info
        invoiceNumber: invoice.invoiceNumber,
        issueDate: invoice.issueDate,
        saleDate: invoice.saleDate,
        dueDate: invoice.dueDate,
        
        // Organization info
        organization: {
          name: orgDetails.name,
          nip: orgDetails.nip || undefined,
          street: orgDetails.street || undefined,
          city: orgDetails.city || undefined,
          postalCode: orgDetails.postalCode || undefined,
          country: orgDetails.country || undefined,
          email: orgDetails.email || undefined,
          phone: orgDetails.phone || undefined,
          logo: orgLogo || undefined,
          legalForm: orgDetails.legalForm || undefined
        },

        // Contractor info
        contractor: invoice.contractor ? {
          fullName: invoice.contractor.fullName,
          nip: invoice.contractor.nip || undefined,
          street: invoice.contractor.street || undefined,
          city: invoice.contractor.city || undefined,
          postalCode: invoice.contractor.postalCode || undefined,
          country: invoice.contractor.country || undefined,
          email: invoice.contractor.email || undefined
        } : undefined,

        // New contractor info (if no existing contractor)
        newContractor: !invoice.contractor ? {
          companyName: invoice.clientName || '',
          nip: invoice.clientVatId || '',
          street: '',
          postalCode: '',
          city: '',
          country: '',
          email: ''
        } : undefined,

        // Line items
        lineItems: invoice.lineItems.map(item => ({
          id: item.id,
          description: item.description,
          quantity: item.quantity,
          unitOfMeasure: item.unitOfMeasure,
          netPrice: item.netPrice,
          vatRate: item.vatRate,
          discount: item.discount || 0,
          netAmount: item.netAmount,
          vatAmount: item.vatAmount,
          grossAmount: item.grossAmount
        })),

        // Totals
        currency: invoice.currency,
        netAmount: invoice.netAmount,
        vatAmount: invoice.vatAmount,
        grossAmount: invoice.grossAmount,

        // Payment info
        paymentMethod: invoice.paymentMethod,
        paymentAccountNumber: invoice.paymentAccountNumber || undefined,
        bankAccount: invoice.paymentAccountNumber || undefined,

        // Additional info
        orderNumber: invoice.orderNumber || '',
        notes: invoice.notes || '',
        attachment: '',
        status: invoice.status,
        documentType: invoice.documentType,
        paymentStatus: invoice.paymentStatus,
        contractorId: invoice.contractorId,
        categoryId: invoice.categoryId
      };

      // Generate PDF
      const pdfGenerator = new InvoicePDFGenerator();
      const pdfBlob = pdfGenerator.generateInvoicePDF(pdfData);
      
      // Convert blob to buffer
      const pdfBuffer = Buffer.from(await pdfBlob.arrayBuffer());
      
      // Generate filename
      const filename = `invoice-${invoice.invoiceNumber.replace(/\//g, '-')}.pdf`;
      
      // Upload to storage
      const storedFile = await fileStorageService.uploadFile(
        pdfBuffer,
        filename,
        'application/pdf'
      );

      // Update invoice with PDF URL
      await prisma.financialDocument.update({
        where: { id: invoiceId },
        data: {
          documentPdfUrl: storedFile.url
        }
      });

      // Revalidate pages
      revalidatePath(`/organizations/${organization.slug}/invoices`);
      revalidatePath(`/organizations/${organization.slug}/invoices/income`);
      revalidatePath(`/organizations/${organization.slug}/invoices/${invoiceId}`);

      return {
        success: true,
        data: {
          pdfUrl: storedFile.url,
          filename: filename
        }
      };

    } catch (error) {
      console.error('Error generating PDF:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate PDF'
      };
    }
  });

// Action to regenerate PDF for an existing invoice
export const regenerateInvoicePdfAction = authOrganizationActionClient
  .metadata({ actionName: 'regenerateInvoicePdfAction' })
  .schema(generatePdfSchema)
  .action(async ({ parsedInput: { invoiceId }, ctx }) => {
    try {
      const { organization } = ctx;

      // Get existing invoice
      const invoice = await prisma.financialDocument.findFirst({
        where: {
          id: invoiceId,
          organizationId: organization.id
        },
        select: {
          documentPdfUrl: true
        }
      });

      if (!invoice) {
        return {
          success: false,
          error: 'Invoice not found'
        };
      }

      // Delete old PDF if it exists
      if (invoice.documentPdfUrl) {
        try {
          // Extract key from URL for local storage
          const url = new URL(invoice.documentPdfUrl);
          const key = url.pathname.substring(1); // Remove leading slash
          await fileStorageService.deleteFile(key);
        } catch (error) {
          console.warn('Failed to delete old PDF:', error);
        }
      }

      // Generate new PDF
      return await generateInvoicePdfAction({ invoiceId });

    } catch (error) {
      console.error('Error regenerating PDF:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to regenerate PDF'
      };
    }
  });
