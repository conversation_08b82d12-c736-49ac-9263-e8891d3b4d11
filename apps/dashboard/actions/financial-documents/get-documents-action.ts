'use server';

import { z } from 'zod';
import {
  getFinancialDocuments,
  getFinancialDocument,
  DocumentType,
  DocumentStatus,
  PaymentStatus
} from '@workspace/database/queries';
import { authOrganizationActionClient } from '~/actions/safe-action';

// Define types for financial document and line item
interface Contractor {
  id: string;
  shortName: string;
  fullName: string;
  [key: string]: unknown;
}

interface Category {
  id: string;
  name: string;
  color: string;
  [key: string]: unknown;
}

interface Transaction {
  id: string;
  amount: number | string;
  description: string;
  [key: string]: unknown;
}

interface Subscription {
  id: string;
  name: string;
  [key: string]: unknown;
}

type FinancialDocumentLineItem = {
  id: string;
  documentId: string;
  description: string;
  quantity: number | string;
  unitPrice: number | string;
  netAmount: number | string;
  vatRate: number | string;
  vatAmount: number | string;
  grossAmount: number | string;
  isBusinessExpense: string;
  createdAt: Date;
  updatedAt: Date;
  document?: FinancialDocument;
};

type FinancialDocument = {
  id: string;
  organizationId: string;
  documentType: string;
  invoiceNumber: string;
  issueDate: Date;
  dueDate: Date;
  saleDate: Date;
  netAmount: number | string;
  vatAmount: number | string;
  vatPercentage: number | string;
  grossAmount: number | string;
  currency: string;
  plnAmount: number | string;
  clientName: string;
  clientVatId: string | null;
  contractorId: string | null;
  paymentMethod: string;
  paymentAccountNumber: string | null;
  paymentDeadlineExceeded: boolean;
  categoryId: string | null;
  isCompanyExpense: string;
  status: string;
  paymentStatus: string;
  documentPdfUrl: string | null;
  sourceType: string;
  sourceDetails: string | null;
  transactionId: string | null;
  subscriptionId: string | null;
  frequency: string | null;
  createdAt: Date;
  updatedAt: Date;
  lineItems: FinancialDocumentLineItem[];
  contractor?: Contractor | null;
  category?: Category | null;
  transaction?: Transaction | null;
  subscription?: Subscription | null;
};

// Schema for getting a list of financial documents
const getDocumentsSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  documentType: z.nativeEnum(DocumentType).optional(),
  status: z.nativeEnum(DocumentStatus).optional(),
  paymentStatus: z.nativeEnum(PaymentStatus).optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  contractorId: z.string().uuid().optional(),
  categoryId: z.string().uuid().optional(),
  search: z.string().optional(),
});

/**
 * Server action for getting a list of financial documents
 */
export const getDocumentsAction = authOrganizationActionClient
  .metadata({ actionName: 'getDocumentsAction' })
  .schema(getDocumentsSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const result = await getFinancialDocuments({
        organizationId: ctx.organization.id,
        ...parsedInput,
      });

      // Serialize Decimal fields for documents
      const documents = result.documents.map((doc: FinancialDocument) => ({
        ...doc,
        netAmount: doc.netAmount?.toString(),
        vatAmount: doc.vatAmount?.toString(),
        vatPercentage: doc.vatPercentage?.toString(),
        grossAmount: doc.grossAmount?.toString(),
        plnAmount: doc.plnAmount?.toString(),
        lineItems: doc.lineItems.map((item: FinancialDocumentLineItem) => ({
          ...item,
          quantity: item.quantity?.toString(),
          unitPrice: item.unitPrice?.toString(),
          netAmount: item.netAmount?.toString(),
          vatRate: item.vatRate?.toString(),
          vatAmount: item.vatAmount?.toString(),
          grossAmount: item.grossAmount?.toString(),
        })),
      }));

      return {
        documents,
        pagination: result.pagination,
      };
    } catch (error) {
      console.error('Error fetching financial documents:', error);
      return {
        documents: [],
        pagination: {
          page: parsedInput.page || 1,
          limit: parsedInput.limit || 10,
          total: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false,
        },
      };
    }
  });

// Schema for getting a single financial document
const getDocumentSchema = z.object({
  id: z.string().uuid(),
});

/**
 * Server action for getting a single financial document
 */
export const getDocumentAction = authOrganizationActionClient
  .metadata({ actionName: 'getDocumentAction' })
  .schema(getDocumentSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const document = await getFinancialDocument(parsedInput.id, ctx.organization.id);

      if (!document) {
        return {
          success: false,
          error: 'Document not found or does not belong to this organization'
        };
      }

      // Serialize Decimal fields
      const serializedDocument = {
        ...document,
        netAmount: document.netAmount?.toString(),
        vatAmount: document.vatAmount?.toString(),
        vatPercentage: document.vatPercentage?.toString(),
        grossAmount: document.grossAmount?.toString(),
        plnAmount: document.plnAmount?.toString(),
        lineItems: document.lineItems.map((item: FinancialDocumentLineItem) => ({
          ...item,
          quantity: item.quantity?.toString(),
          unitPrice: item.unitPrice?.toString(),
          netAmount: item.netAmount?.toString(),
          vatRate: item.vatRate?.toString(),
          vatAmount: item.vatAmount?.toString(),
          grossAmount: item.grossAmount?.toString(),
        })),
      };

      return { success: true, document: serializedDocument };
    } catch (error) {
      console.error('Error fetching financial document:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch financial document'
      };
    }
  });
