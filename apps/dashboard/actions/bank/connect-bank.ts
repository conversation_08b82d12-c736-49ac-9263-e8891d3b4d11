'use server';

import { revalidatePath } from 'next/cache';

import { createBankConnection } from '@workspace/database/queries/bank-connections';
import { BankConnectionStatus } from '@workspace/database/queries/bank-connection-status';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { connectBankSchema } from '~/schemas/bank/connect-bank-schema';
import { getAccessToken, createRequisition, createEndUserAgreement } from '~/services/gocardless/api';

/**
 * Server action for connecting to a bank via GoCardless
 *
 * This action creates a requisition in GoCardless and stores the connection details
 * in the database. It returns a link URL that the user can use to authorize the connection.
 */
export const connectBank = authOrganizationActionClient
  .metadata({ actionName: 'connectBank' })
  .schema(connectBankSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      // Use the NEXT_PUBLIC_DASHBOARD_URL from environment variables
      const baseUrl = process.env.NEXT_PUBLIC_DASHBOARD_URL;

      if (!baseUrl) {
        throw new Error('NEXT_PUBLIC_DASHBOARD_URL is not configured in environment variables');
      }

      // Create the callback URL with proper encoding
      // Make sure to use the full absolute URL that GoCardless can reach
      // We'll rely on the our_ref parameter which will be added by createRequisition
      // GoCardless will add this parameter automatically in the callback
      const callbackUrl = `${baseUrl}/api/bank/callback?slug=${encodeURIComponent(ctx.organization.slug)}`;

      console.log('Using callback URL:', callbackUrl);

      // Get access token for GoCardless API
      const { accessToken } = await getAccessToken();

      console.log('Successfully obtained access token for GoCardless');

      // Create an end user agreement first
      let agreementId;
      try {
        const agreement = await createEndUserAgreement(
          accessToken,
          parsedInput.institutionId,
          90, // 90 days of transaction history (bank's limit)
          90, // 90 days of access
          ['balances', 'details', 'transactions'] // Full access scope
        );
        agreementId = agreement.id;
        console.log('Created end user agreement with ID:', agreementId);
      } catch (agreementError: unknown) {
        console.error('Error creating end user agreement:', agreementError);
        // Continue without agreement - will use default terms
        console.log('Continuing without explicit agreement, using default terms');
      }

      // Ensure we have a reference for the bank connection
      // This is crucial for the callback to identify the connection
      const reference = parsedInput.reference || `bank-connection-${Date.now()}`;
      console.log(`Using reference for bank connection: ${reference}`);

      // Create requisition in GoCardless
      const requisition = await createRequisition(
        accessToken,
        parsedInput.institutionId,
        callbackUrl,
        reference, // Always pass a reference
        agreementId, // Pass the agreement ID if we have one
        parsedInput.userLanguage
      );

      // Validate requisition response
      if (!requisition) {
        console.error('Empty requisition response from GoCardless');
        throw new Error('Empty response from GoCardless API');
      }

      if (!requisition.id) {
        console.error('Missing requisition ID in response:', requisition);
        throw new Error('Invalid response from GoCardless API: Missing requisition ID');
      }

      if (!requisition.link) {
        console.error('Missing link URL in response:', requisition);
        throw new Error('Invalid response from GoCardless API: Missing link URL');
      }

      console.log('Successfully created requisition with ID:', requisition.id);
      console.log('Requisition details:', {
        id: requisition.id,
        status: requisition.status?.short || 'CREATED',
        hasLink: !!requisition.link,
        linkUrlStart: requisition.link ? requisition.link.substring(0, 50) + '...' : 'N/A'
      });

      // Store the connection in the database immediately with PENDING status
      // This will allow the callback to find the connection by requisition ID
      try {
        console.log('Storing bank connection in database with PENDING status');

        // Calculate expiration date if we have an agreement with access_valid_for_days
        let expiresAt = null;
        if (agreementId && requisition.agreement) {
          // Try to get access_valid_for_days from the requisition.agreement if available
          const accessValidForDays = requisition.agreement.access_valid_for_days || 90;
          expiresAt = new Date();
          expiresAt.setDate(expiresAt.getDate() + parseInt(accessValidForDays.toString(), 10));
          console.log(`Setting connection expiration date to ${expiresAt.toISOString()} (${accessValidForDays} days from now)`);
        }

        await createBankConnection({
          organizationId: ctx.organization.id,
          requisitionId: requisition.id,
          institutionId: parsedInput.institutionId,
          status: BankConnectionStatus.PENDING,
          reference: reference, // Use the same reference we passed to createRequisition
          accessToken,
          refreshToken: '', // We don't have a refresh token yet
          linkUrl: requisition.link,
          agreementId, // Store the agreement ID
          expiresAt   // Store the expiration date
        });

        console.log('Successfully stored bank connection in database');

        // Revalidate the transactions page to show the pending connection
        revalidatePath(`/organizations/${ctx.organization.slug}/transactions`);
      } catch (dbError) {
        console.error('Error storing bank connection in database:', dbError);
        // Continue even if database storage fails
        // The callback will create the connection if it doesn't exist
      }

      // Return the link URL for the user to authorize the connection
      return {
        linkUrl: requisition.link,
        requisitionId: requisition.id,
        success: true
      };
    } catch (error) {
      console.error('Error connecting to bank:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Instead of throwing an error, return a structured error response
      // This allows the client to handle the error more gracefully
      return {
        error: `Failed to connect to bank: ${errorMessage}`,
        success: false
      };
    }
  });
