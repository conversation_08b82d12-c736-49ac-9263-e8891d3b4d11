'use server';

import { revalidatePath } from 'next/cache';

import { NotFoundError } from '@workspace/common/errors';
import { prisma } from '@workspace/database/client';
import {
  getBankConnection,
  deleteBankConnection
} from '@workspace/database/queries/bank-connections';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  getAccessToken,
  deleteRequisition,
  getEndUserAgreements,
  deleteEndUserAgreement
} from '~/services/gocardless/api';

import { z } from 'zod';

/**
 * Schema for revoking a bank connection
 */
const revokeBankConnectionSchema = z.object({
  bankConnectionId: z.string(),
  deleteTransactions: z.boolean()
});

/**
 * Server action for revoking a bank connection
 *
 * This action deletes the bank connection from GoCardless and the database.
 * It can also delete associated bank accounts and transactions.
 */
export const revokeBankConnection = authOrganizationActionClient
  .metadata({ actionName: 'revokeBankConnection' })
  .schema(revokeBankConnectionSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      console.log(`Revoking bank connection: ${parsedInput.bankConnectionId}, deleteTransactions: ${parsedInput.deleteTransactions}`);

      // Get the bank connection from the database
      const connection = await getBankConnection(parsedInput.bankConnectionId);
      if (!connection) {
        console.error(`Bank connection not found: ${parsedInput.bankConnectionId}`);
        throw new NotFoundError('Bank connection not found');
      }

      // Verify that the connection belongs to the organization
      if (connection.organizationId !== ctx.organization.id) {
        console.error(`Bank connection ${parsedInput.bankConnectionId} does not belong to organization ${ctx.organization.id}`);
        throw new NotFoundError('Bank connection not found');
      }

      // Get access token for GoCardless API
      let accessToken;
      try {
        const tokenResponse = await getAccessToken();
        accessToken = tokenResponse.accessToken;
        console.log('Successfully obtained GoCardless access token');
      } catch (tokenError) {
        console.error('Failed to get GoCardless access token:', tokenError);
        // Continue with deletion even if we can't get a token
        console.log('Proceeding with bank connection deletion despite token error');
      }

      // Try to delete the requisition in GoCardless if we have a token
      let gocardlessDeleted = false;
      if (accessToken) {
        try {
          // First, delete the requisition
          await deleteRequisition(accessToken, connection.requisitionId);
          console.log(`Successfully deleted requisition ${connection.requisitionId} in GoCardless`);
          gocardlessDeleted = true;

          // Then, try to find and delete any associated end user agreements
          try {
            console.log('Looking for end user agreements to clean up...');
            const agreements = await getEndUserAgreements(accessToken);

            if (agreements && agreements.length > 0) {
              console.log(`Found ${agreements.length} end user agreements. Checking for matches...`);

              // Look for agreements that might be associated with this connection
              // Since we don't store agreement IDs, we'll try to delete all agreements
              // This is a best-effort approach
              for (const agreement of agreements) {
                try {
                  console.log(`Attempting to delete agreement ${agreement.id}`);
                  await deleteEndUserAgreement(accessToken, agreement.id);
                } catch (agreementError) {
                  console.warn(`Failed to delete agreement ${agreement.id}:`, agreementError);
                  // Continue with other agreements even if one fails
                }
              }
            } else {
              console.log('No end user agreements found to clean up');
            }
          } catch (agreementsError) {
            console.warn('Failed to clean up end user agreements:', agreementsError);
            // Continue with deletion even if agreement cleanup fails
          }
        } catch (error) {
          // Check if the error indicates the requisition doesn't exist (already deleted or not found)
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (errorMessage.includes('404') || errorMessage.includes('not found')) {
            console.log(`Requisition ${connection.requisitionId} already deleted or not found in GoCardless`);
            gocardlessDeleted = true;
          } else {
            // If the requisition doesn't exist or can't be deleted, continue
            console.warn(`Failed to delete requisition ${connection.requisitionId} in GoCardless:`, error);
          }
        }
      }

      // Delete the connection from the database
      console.log(`Deleting bank connection ${connection.id} from database`);

      try {
        const deleteResult = await deleteBankConnection(connection.id);

        if (!deleteResult.success) {
          console.error(`Failed to delete bank connection ${connection.id}:`, deleteResult);

          // If GoCardless was successfully deleted but database deletion failed,
          // we should force mark the connection as deleted in the database
          if (gocardlessDeleted) {
            console.log(`GoCardless requisition was deleted but database deletion failed. Forcing status update.`);
            try {
              // Force update the connection status to DELETED
              await prisma.bankConnection.update({
                where: { id: connection.id },
                data: {
                  status: 'DELETED',
                  updatedAt: new Date()
                }
              });

              console.log(`Successfully marked bank connection ${connection.id} as DELETED`);

              // Revalidate the transactions page to reflect the changes
              try {
                revalidatePath(`/organizations/${ctx.organization.slug}/transactions`);
              } catch (revalidateError) {
                console.error('Error revalidating path:', revalidateError);
              }

              return {
                success: true,
                message: 'Bank connection marked as deleted'
              };
            } catch (updateError) {
              console.error(`Failed to mark bank connection ${connection.id} as DELETED:`, updateError);

              // Even if the update fails, if GoCardless was deleted, consider it a success
              // This prevents the UI from showing an error when the connection is actually gone
              return {
                success: true,
                message: 'Bank connection removed from GoCardless'
              };
            }
          }

          return {
            success: false,
            error: deleteResult.error || 'Failed to delete bank connection from database'
          };
        }
      } catch (deleteError) {
        console.error(`Error during deleteBankConnection for ${connection.id}:`, deleteError);

        // If GoCardless was deleted, consider it a partial success
        if (gocardlessDeleted) {
          return {
            success: true,
            message: 'Bank connection removed from GoCardless but database update failed'
          };
        }

        return {
          success: false,
          error: 'Database error during bank connection deletion'
        };
      }

      console.log(`Bank connection ${connection.id} successfully deleted`);

      // Revalidate the transactions page to reflect the changes
      try {
        revalidatePath(`/organizations/${ctx.organization.slug}/transactions`);
        console.log(`Revalidated path: /organizations/${ctx.organization.slug}/transactions`);
      } catch (revalidateError) {
        console.error('Error revalidating path:', revalidateError);
        // Continue even if revalidation fails
      }

      return {
        success: true
      };
    } catch (error) {
      console.error('Error revoking bank connection:', error);

      // Return a more specific error message if possible
      if (error instanceof NotFoundError) {
        return {
          success: false,
          error: error.message
        };
      }

      return {
        success: false,
        error: 'Failed to revoke bank connection. Please try again.'
      };
    }
  });
