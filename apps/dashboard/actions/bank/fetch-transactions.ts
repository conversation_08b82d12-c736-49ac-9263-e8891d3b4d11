'use server';

import { revalidatePath } from 'next/cache';
import { z } from 'zod';

import { NotFoundError } from '@workspace/common/errors';
import {
  getBankConnection,
  updateBankConnection
} from '@workspace/database/queries/bank-connections';
import {
  createBankTransaction,
  getBankTransactionByTransactionId
} from '@workspace/database/queries/bank-transactions';
import { importBankTransactionsToMain } from '@workspace/database/queries/bank-transactions-import';
import { updateBankAccount } from '@workspace/database/queries/bank-accounts';
import { replaceOrgSlug, routes } from '@workspace/routes';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { getAccessToken, getAccountTransactions, refreshAccessToken } from '~/services/gocardless/api';

/**
 * Schema for fetching bank transactions
 */
const fetchTransactionsSchema = z.object({
  bankConnectionId: z.string().min(1, 'Bank connection ID is required'),
  importToMain: z.boolean().default(false),
  sourceId: z.string().min(1, 'Source ID is required'),
  dateFrom: z.date().optional(),
  dateTo: z.date().optional()
});

/**
 * Server action for fetching transactions from a connected bank account
 *
 * This action fetches transactions from GoCardless and stores them in the database.
 * It can also import the transactions to the main Transaction table.
 */
export const fetchTransactions = authOrganizationActionClient
  .metadata({ actionName: 'fetchTransactions' })
  .schema(fetchTransactionsSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      console.log('Fetching transactions with params:', {
        bankConnectionId: parsedInput.bankConnectionId,
        importToMain: parsedInput.importToMain,
        sourceId: parsedInput.sourceId,
        dateFrom: parsedInput.dateFrom,
        dateTo: parsedInput.dateTo
      });

      // Get the bank connection from the database
      const connection = await getBankConnection(parsedInput.bankConnectionId);
      if (!connection) {
        console.error(`Bank connection not found: ${parsedInput.bankConnectionId}`);
        throw new NotFoundError('Bank connection not found');
      }

      // Verify that the connection belongs to the organization
      if (connection.organizationId !== ctx.organization.id) {
        console.error(`Bank connection belongs to different organization: ${connection.organizationId} vs ${ctx.organization.id}`);
        throw new NotFoundError('Bank connection not found');
      }

      // If the connection has no bank accounts, return an error
      if (!connection.bankAccounts || connection.bankAccounts.length === 0) {
        console.error(`No bank accounts found for connection: ${connection.id}`);
        throw new Error('No bank accounts found for this connection');
      }

      console.log(`Found ${connection.bankAccounts.length} bank accounts for connection ${connection.id}`);

      // Get access token for GoCardless API
      let accessToken = connection.accessToken;
      let tokenRefreshed = false;

      // If the token might be expired, refresh it
      if (connection.refreshToken) {
        try {
          console.log('Attempting to refresh access token');
          const { accessToken: newToken } = await refreshAccessToken(connection.refreshToken);
          accessToken = newToken;
          tokenRefreshed = true;

          // Update the connection with the new token
          await updateBankConnection(connection.id, {
            accessToken: newToken
          });

          console.log('Successfully refreshed access token');
        } catch (refreshError) {
          console.error('Error refreshing token:', refreshError);
          // If refresh fails, get a new token
          try {
            console.log('Getting new access token');
            const { accessToken: newToken } = await getAccessToken();
            accessToken = newToken;
            tokenRefreshed = true;

            // Update the connection with the new token
            await updateBankConnection(connection.id, {
              accessToken: newToken
            });

            console.log('Successfully got new access token');
          } catch (tokenError) {
            console.error('Error getting new access token:', tokenError);
            throw new Error('Failed to get access token. Please try again later.');
          }
        }
      } else {
        // If no refresh token, get a new token
        try {
          console.log('No refresh token available, getting new access token');
          const { accessToken: newToken } = await getAccessToken();
          accessToken = newToken;
          tokenRefreshed = true;

          // Update the connection with the new token
          await updateBankConnection(connection.id, {
            accessToken: newToken
          });

          console.log('Successfully got new access token');
        } catch (tokenError) {
          console.error('Error getting new access token:', tokenError);
          throw new Error('Failed to get access token. Please try again later.');
        }
      }

      // Fetch and store transactions for each bank account
      const results = [];
      let totalTransactionsCount = 0;

      for (const account of connection.bankAccounts) {
        try {
          console.log(`Processing bank account: ${account.id} (${account.name || account.accountId})`);

          // Fetch transactions from GoCardless with date filtering
          const transactions = await getAccountTransactions(
            accessToken,
            account.accountId,
            parsedInput.dateFrom || new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // Default to 90 days ago
            parsedInput.dateTo || new Date()
          );

          if (!transactions.transactions || !Array.isArray(transactions.transactions)) {
            console.error('Invalid transactions response:', transactions);
            throw new Error('Invalid response from GoCardless API');
          }

          console.log(`Fetched ${transactions.transactions.length} transactions for account ${account.id}`);
          totalTransactionsCount += transactions.transactions.length;

          // Store each transaction in the database
          let storedCount = 0;
          for (const transaction of transactions.transactions) {
            try {
              // Check if transaction already exists to avoid duplicates
              const existingTransaction = await getBankTransactionByTransactionId(
                account.id,
                transaction.transactionId
              );

              if (existingTransaction) {
                console.log(`Transaction ${transaction.transactionId} already exists, skipping`);
                continue;
              }

              // Create the transaction
              await createBankTransaction({
                bankAccountId: account.id,
                transactionId: transaction.transactionId,
                bookingDate: new Date(transaction.bookingDate),
                valueDate: transaction.valueDate ? new Date(transaction.valueDate) : undefined,
                amount: transaction.transactionAmount.amount,
                currency: transaction.transactionAmount.currency,
                description: transaction.remittanceInformationUnstructured,
                creditorName: transaction.creditorName,
                debtorName: transaction.debtorName,
                status: 'BOOKED'
              });

              storedCount++;
            } catch (transactionError) {
              console.error(`Error storing transaction ${transaction.transactionId}:`, transactionError);
              // Continue with next transaction
            }
          }

          console.log(`Stored ${storedCount} new transactions for account ${account.id}`);

          // Update the last synced timestamp for the account
          await updateBankAccount(account.id, {
            lastSynced: new Date()
          });

          // Import transactions to main table if requested
          if (parsedInput.importToMain) {
            try {
              console.log(`Importing transactions to main table for account ${account.id}`);
              const importedTransactions = await importBankTransactionsToMain(
                account.id,
                ctx.organization.id,
                parsedInput.sourceId
              );
              console.log(`Imported ${importedTransactions.length} transactions to main table`);
            } catch (importError) {
              console.error(`Error importing transactions to main table:`, importError);
              // Continue with next account
            }
          }

          results.push({
            accountId: account.id,
            accountName: account.name || account.accountId,
            transactionsCount: transactions.transactions.length,
            newTransactionsCount: storedCount
          });
        } catch (accountError: unknown) {
          const errorMessage = accountError instanceof Error
            ? accountError.message
            : 'Unknown error processing account';
          console.error(`Error processing account ${account.id}:`, accountError);
          results.push({
            accountId: account.id,
            accountName: account.name || account.accountId,
            error: errorMessage,
            transactionsCount: 0,
            newTransactionsCount: 0
          });
          // Continue with next account
        }
      }

      // Revalidate the transactions page to show the new transactions
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.Transactions,
          ctx.organization.slug
        )
      );

      console.log(`Finished fetching transactions. Total: ${totalTransactionsCount}`);

      return {
        success: true,
        results,
        totalTransactionsCount,
        tokenRefreshed
      };
    } catch (error) {
      console.error('Error fetching bank transactions:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch bank transactions'
      };
    }
  });
