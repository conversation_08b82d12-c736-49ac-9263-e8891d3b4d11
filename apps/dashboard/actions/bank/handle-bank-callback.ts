'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';

import { NotFoundError } from '@workspace/common/errors';
import {
  getBankConnectionByRequisitionId,
  updateBankConnection
} from '@workspace/database/queries/bank-connections';
import { createBankAccount } from '@workspace/database/queries/bank-accounts';
import { replaceOrgSlug, routes } from '@workspace/routes';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  getAccessToken,
  getRequisition,
  getAccountDetails
} from '~/services/gocardless/api';

/**
 * Schema for handling bank callbacks
 */
const handleBankCallbackSchema = {
  requisitionId: String,
  organizationId: String
};

/**
 * Server action for handling callbacks from GoCardless after bank authorization
 *
 * This action updates the bank connection status and fetches the bank accounts
 * associated with the connection.
 */
export const handleBankCallback = authOrganizationActionClient
  .metadata({ actionName: 'handleBankCallback' })
  .schema(handleBankCallbackSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      // Verify that the organization ID matches
      if (parsedInput.organizationId !== ctx.organization.id) {
        throw new NotFoundError('Organization not found');
      }

      // Get the bank connection from the database
      const connection = await getBankConnectionByRequisitionId(parsedInput.requisitionId);
      if (!connection) {
        throw new NotFoundError('Bank connection not found');
      }

      // Get access token for GoCardless API
      const { accessToken, refreshToken } = await getAccessToken();

      // Get requisition details from GoCardless
      const requisition = await getRequisition(accessToken, parsedInput.requisitionId);

      // Update the bank connection in the database
      await updateBankConnection(connection.id, {
        status: requisition.status?.short || 'UNKNOWN',
        accessToken,
        refreshToken,
        linkUrl: requisition.link
      });

      // If the requisition has accounts, create them in the database
      if (requisition.accounts && requisition.accounts.length > 0) {
        console.log(`Processing ${requisition.accounts.length} accounts from requisition ${parsedInput.requisitionId}`);

        for (const accountId of requisition.accounts) {
          try {
            console.log(`Processing account ID: ${accountId}`);

            // Get account details from GoCardless
            const accountDetails = await getAccountDetails(accessToken, accountId);

            // Extract account information, handling both possible response structures
            const iban = accountDetails.account?.iban || accountDetails.iban || null;
            const bban = accountDetails.account?.bban || accountDetails.bban || null;
            const ownerName = accountDetails.account?.ownerName || accountDetails.owner_name || null;
            const name = accountDetails.account?.name || accountDetails.name ||
                         accountDetails.account?.product || accountDetails.product || 'Bank Account';

            console.log(`Creating bank account with details:`, {
              accountId,
              iban: iban ? `${iban.substring(0, 4)}...` : null, // Log partial IBAN for privacy
              hasName: !!name,
              hasOwnerName: !!ownerName
            });

            // Create the bank account in the database
            const bankAccount = await createBankAccount({
              bankConnectionId: connection.id,
              accountId,
              iban,
              bban,
              status: 'ACTIVE',
              ownerName,
              name
            });

            console.log(`Successfully created bank account with ID: ${bankAccount.id}`);
          } catch (accountError) {
            console.error(`Error processing account ${accountId}:`, accountError);
            // Continue with next account instead of failing the whole process
          }
        }
      } else {
        console.warn(`No accounts found in requisition ${parsedInput.requisitionId}`);
      }

      // Revalidate the transactions page to show the updated connection
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.transactions,
          ctx.organization.slug
        )
      );

      // Redirect to the transactions page
      redirect(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.transactions,
          ctx.organization.slug
        )
      );
    } catch (error) {
      console.error('Error handling bank callback:', error);
      throw new Error('Failed to complete bank connection. Please try again.');
    }
  });
