'use server';

import { revalidateTag } from 'next/cache';

import { prisma } from '@workspace/database/client';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { Caching, OrganizationCacheKey } from '~/data/caching';
import { createCategorySchema } from '~/schemas/categories/create-category-schema';

export const createCategory = authOrganizationActionClient
  .metadata({ actionName: 'createCategory' })
  .schema(createCategorySchema)
  .action(async ({ parsedInput, ctx }) => {
    // Use Prisma client directly without type assertion
    await prisma.category.create({
      data: {
        name: parsedInput.name,
        description: parsedInput.description,
        color: parsedInput.color,
        aiInformation: parsedInput.aiInformation,
        organizationId: ctx.organization.id
      },
      select: {
        id: true // SELECT NONE
      }
    });

    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.Categories,
        ctx.organization.id
      )
    );
  });
