'use server';

import { revalidateTag } from 'next/cache';

import { prisma } from '@workspace/database/client';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { Caching, OrganizationCacheKey } from '~/data/caching';
import { NotFoundError } from '@workspace/common/errors';
import { updateCategorySchema } from '~/schemas/categories/update-category-schema';

export const updateCategory = authOrganizationActionClient
  .metadata({ actionName: 'updateCategory' })
  .schema(updateCategorySchema)
  .action(async ({ parsedInput, ctx }) => {
    // Use Prisma client directly without type assertion
    const count = await prisma.category.count({
      where: {
        organizationId: ctx.organization.id,
        id: parsedInput.id
      }
    });
    if (count < 1) {
      throw new NotFoundError('Kategoria nie została znaleziona');
    }

    // Use Prisma client directly without type assertion
    await prisma.category.update({
      where: { id: parsedInput.id },
      data: {
        name: parsedInput.name,
        description: parsedInput.description,
        color: parsedInput.color,
        aiInformation: parsedInput.aiInformation
      },
      select: {
        id: true // SELECT NONE
      }
    });

    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.Categories,
        ctx.organization.id
      )
    );
  });
