'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { getCategories as getCategoriesQuery } from '@workspace/database/queries';
import { replaceOrgSlug, routes } from '@workspace/routes';
import { authOrganizationActionClient } from '~/actions/safe-action';
import { getAuthOrganizationContext } from '@workspace/auth/context';

/**
 * Server action for getting all categories for an organization
 */
export async function getCategories() {
  try {
    // Get the current session and organization
    const ctx = await getAuthOrganizationContext();

    if (!ctx.organization) {
      return [];
    }

    // Get categories from the database
    const categories = await getCategoriesQuery(ctx.organization.id);

    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// Schema for creating a category
const createCategorySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Must be a valid hex color'),
  aiInformation: z.string().optional(),
});

/**
 * Server action for creating a category
 */
export const createCategoryAction = authOrganizationActionClient
  .metadata({ actionName: 'createCategoryAction' })
  .schema(createCategorySchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      // Import the createCategory function dynamically to avoid circular dependencies
      const { createCategory } = await import('@workspace/database/queries');

      const category = await createCategory({
        organizationId: ctx.organization.id,
        ...parsedInput,
      });

      // Revalidate the categories page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.Categories,
          ctx.organization.slug
        )
      );

      return { success: true, category };
    } catch (error) {
      console.error('Error creating category:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create category'
      };
    }
  });

// Schema for updating a category
const updateCategorySchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Must be a valid hex color').optional(),
  aiInformation: z.string().optional(),
});

/**
 * Server action for updating a category
 */
export const updateCategoryAction = authOrganizationActionClient
  .metadata({ actionName: 'updateCategoryAction' })
  .schema(updateCategorySchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      // Import the updateCategory function dynamically to avoid circular dependencies
      const { updateCategory, getCategory } = await import('@workspace/database/queries');

      const { id, ...data } = parsedInput;

      // Verify the category belongs to the organization
      const existingCategory = await getCategory(id);

      if (!existingCategory || existingCategory.organizationId !== ctx.organization.id) {
        return {
          success: false,
          error: 'Category not found or does not belong to this organization'
        };
      }

      // Update the category
      const category = await updateCategory(id, data);

      // Revalidate the categories page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.Categories,
          ctx.organization.slug
        )
      );

      return { success: true, category };
    } catch (error) {
      console.error('Error updating category:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update category'
      };
    }
  });

// Schema for deleting a category
const deleteCategorySchema = z.object({
  id: z.string().uuid(),
});

/**
 * Server action for deleting a category
 */
export const deleteCategoryAction = authOrganizationActionClient
  .metadata({ actionName: 'deleteCategoryAction' })
  .schema(deleteCategorySchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      // Import the deleteCategory function dynamically to avoid circular dependencies
      const { deleteCategory, getCategory } = await import('@workspace/database/queries');

      const { id } = parsedInput;

      // Verify the category belongs to the organization
      const existingCategory = await getCategory(id);

      if (!existingCategory || existingCategory.organizationId !== ctx.organization.id) {
        return {
          success: false,
          error: 'Category not found or does not belong to this organization'
        };
      }

      // Delete the category
      await deleteCategory(id);

      // Revalidate the categories page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.Categories,
          ctx.organization.slug
        )
      );

      return { success: true };
    } catch (error) {
      console.error('Error deleting category:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete category'
      };
    }
  });
