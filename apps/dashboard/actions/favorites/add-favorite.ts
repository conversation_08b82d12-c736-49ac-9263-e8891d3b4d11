'use server';

import { revalidateTag } from 'next/cache';

import { prisma } from '@workspace/database/client';

import { updateFavoritesOrder } from '~/actions/favorites/_favorites-order';
import { authOrganizationActionClient } from '~/actions/safe-action';
import { Caching, OrganizationCacheKey } from '~/data/caching';
import { addFavoriteSchema } from '~/schemas/favorites/add-favorite-schema';

/**
 * Add a favorite item
 * This is a simplified version that doesn't use contacts
 * since the contacts functionality has been removed
 */
export const addFavorite = authOrganizationActionClient
  .metadata({ actionName: 'addFavorite' })
  .schema(addFavoriteSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Create a favorite for a subscription or category instead of a contact
    await prisma.favorite.create({
      data: {
        userId: ctx.session.user.id,
        order: await prisma.favorite.count({
          where: { userId: ctx.session.user.id }
        })
      },
      select: {
        id: true // SELECT NONE
      }
    });

    await updateFavoritesOrder(ctx.session.user.id);

    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.Favorites,
        ctx.organization.id,
        ctx.session.user.id
      )
    );
  });
