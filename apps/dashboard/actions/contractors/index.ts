'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import {
  getContractors as getContractorsQuery,
  getContractor as getContractorQuery
} from '@workspace/database/queries';
import { replaceOrgSlug, routes } from '@workspace/routes';
import { authOrganizationActionClient } from '~/actions/safe-action';
import { getAuthOrganizationContext } from '@workspace/auth/context';

/**
 * Server action for getting all contractors for an organization
 */
export async function getContractorsServer() {
  try {
    // Get the current session and organization
    const ctx = await getAuthOrganizationContext();

    if (!ctx.organization) {
      return [];
    }

    // Get contractors from the database
    const contractors = await getContractorsQuery(ctx.organization.id);

    return contractors;
  } catch (error) {
    console.error('Error fetching contractors:', error);
    return [];
  }
}

// Schema for getting a single contractor
const getContractorSchema = z.object({
  id: z.string().uuid(),
});

/**
 * Server action for getting a single contractor
 */
export const getContractorAction = authOrganizationActionClient
  .metadata({ actionName: 'getContractorAction' })
  .schema(getContractorSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const contractor = await getContractorQuery(parsedInput.id);

      if (!contractor || contractor.organizationId !== ctx.organization.id) {
        return {
          success: false,
          error: 'Contractor not found or does not belong to this organization'
        };
      }

      return { success: true, contractor };
    } catch (error) {
      console.error('Error fetching contractor:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch contractor'
      };
    }
  });

// Schema for creating a contractor
const createContractorSchema = z.object({
  shortName: z.string().optional(),
  fullName: z.string().min(1, 'Full name is required'),
  nip: z.string().optional(),
  regon: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  city: z.string().optional(),
  street: z.string().optional(),
  email: z.string().email().optional().nullable(),
  phoneNumber: z.string().optional().nullable(),
});

/**
 * Server action for creating a contractor
 */
export const createContractorAction = authOrganizationActionClient
  .metadata({ actionName: 'createContractorAction' })
  .schema(createContractorSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      // Import the createContractor function dynamically to avoid circular dependencies
      const { createContractor } = await import('@workspace/database/queries');

      const contractor = await createContractor({
        organizationId: ctx.organization.id,
        ...parsedInput,
      });

      // Revalidate the contractors page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.Contractors,
          ctx.organization.slug
        )
      );

      return { success: true, contractor };
    } catch (error) {
      console.error('Error creating contractor:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create contractor'
      };
    }
  });

// Schema for updating a contractor
const updateContractorSchema = z.object({
  id: z.string().uuid(),
  shortName: z.string().optional(),
  fullName: z.string().min(1, 'Full name is required').optional(),
  nip: z.string().optional(),
  regon: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  city: z.string().optional(),
  street: z.string().optional(),
  email: z.string().email().optional().nullable(),
  phoneNumber: z.string().optional().nullable(),
});

/**
 * Server action for updating a contractor
 */
export const updateContractorAction = authOrganizationActionClient
  .metadata({ actionName: 'updateContractorAction' })
  .schema(updateContractorSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      // Import the updateContractor function dynamically to avoid circular dependencies
      const { updateContractor, getContractor } = await import('@workspace/database/queries');

      const { id, ...data } = parsedInput;

      // Verify the contractor belongs to the organization
      const existingContractor = await getContractor(id);

      if (!existingContractor || existingContractor.organizationId !== ctx.organization.id) {
        return {
          success: false,
          error: 'Contractor not found or does not belong to this organization'
        };
      }

      // Update the contractor
      const contractor = await updateContractor(id, data);

      // Revalidate the contractors page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.Contractors,
          ctx.organization.slug
        )
      );

      return { success: true, contractor };
    } catch (error) {
      console.error('Error updating contractor:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update contractor'
      };
    }
  });

// Schema for deleting a contractor
const deleteContractorSchema = z.object({
  id: z.string().uuid(),
});

/**
 * Server action for deleting a contractor
 */
export const deleteContractorAction = authOrganizationActionClient
  .metadata({ actionName: 'deleteContractorAction' })
  .schema(deleteContractorSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      // Import the deleteContractor function dynamically to avoid circular dependencies
      const { deleteContractor, getContractor } = await import('@workspace/database/queries');

      const { id } = parsedInput;

      // Verify the contractor belongs to the organization
      const existingContractor = await getContractor(id);

      if (!existingContractor || existingContractor.organizationId !== ctx.organization.id) {
        return {
          success: false,
          error: 'Contractor not found or does not belong to this organization'
        };
      }

      // Delete the contractor
      await deleteContractor(id);

      // Revalidate the contractors page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.Contractors,
          ctx.organization.slug
        )
      );

      return { success: true };
    } catch (error) {
      console.error('Error deleting contractor:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete contractor'
      };
    }
  });
