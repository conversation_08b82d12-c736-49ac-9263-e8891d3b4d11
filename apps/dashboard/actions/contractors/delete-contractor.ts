'use server';

import { revalidatePath } from 'next/cache';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';
import { z } from 'zod';

import { routes, replaceOrgSlug } from '@workspace/routes';

// Input schema
const deleteContractorSchema = z.object({
  id: z.string().uuid('Invalid contractor ID')
});

// Return schema (not enforced by the action client, but useful for TypeScript)
export type DeleteContractorResult = {
  success: boolean;
  hasSubscriptions?: boolean;
  subscriptionsCount?: number;
};

export const deleteContractor = authOrganizationActionClient
  .metadata({ actionName: 'deleteContractor' })
  .schema(deleteContractorSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      console.log('Deleting contractor with ID:', parsedInput.id);
      console.log('Organization ID:', ctx.organization.id);

      // Find the contractor to delete
      const contractor = await prisma.contractor.findUnique({
        where: {
          id: parsedInput.id,
          organizationId: ctx.organization.id
        },
        include: {
          subscriptions: true
        }
      });

      if (!contractor) {
        console.error(`Contractor not found with ID: ${parsedInput.id} in organization: ${ctx.organization.id}`);
        throw new NotFoundError('Contractor not found');
      }

      console.log('Found contractor:', contractor.fullName);

      // If contractor has subscriptions, update them to store contractor info directly
      if (contractor.subscriptions.length > 0) {
        console.log(`Contractor has ${contractor.subscriptions.length} subscriptions. Updating them...`);

        // Prepare address string
        const addressParts = [];
        if (contractor.street) addressParts.push(contractor.street);
        if (contractor.postalCode || contractor.city) {
          const cityPart = [contractor.postalCode, contractor.city].filter(Boolean).join(' ');
          if (cityPart) addressParts.push(cityPart);
        }
        if (contractor.country) addressParts.push(contractor.country);
        const addressString = addressParts.join(', ');

        // Update all subscriptions to store contractor info directly
        await prisma.$transaction(
          contractor.subscriptions.map((subscription) =>
            prisma.subscription.update({
              where: { id: subscription.id },
              data: {
                company: contractor.fullName,
                companyNip: contractor.nip,
                companyAddress: addressString || null,
                contractorId: null
              }
            })
          )
        );
      }

      // Delete the contractor
      await prisma.contractor.delete({
        where: {
          id: parsedInput.id
        }
      });

      console.log('Contractor deleted successfully');

      // Revalidate contractors page
      revalidatePath(
        replaceOrgSlug(routes.dashboard.organizations.slug.Contractors, ctx.organization.slug)
      );

      // Also revalidate subscriptions page if there were subscriptions
      if (contractor.subscriptions.length > 0) {
        revalidatePath(
          replaceOrgSlug(routes.dashboard.organizations.slug.Subscriptions, ctx.organization.slug)
        );
      }

      return {
        success: true,
        hasSubscriptions: contractor.subscriptions.length > 0,
        subscriptionsCount: contractor.subscriptions.length
      };
    } catch (error) {
      console.error('Error deleting contractor:', error);

      // Provide more detailed error information
      if (error instanceof NotFoundError) {
        throw error;
      } else {
        // Try to get more detailed error information
        let errorDetails = 'Unknown error';
        try {
          if (error instanceof Error) {
            errorDetails = error.message;
            console.error('Error message:', error.message);
            console.error('Error stack:', error.stack);
          }

          // Try to stringify the error for more details
          const errorJson = JSON.stringify(error, Object.getOwnPropertyNames(error), 2);
          console.error('Detailed error:', errorJson);
        } catch (jsonError) {
          console.error('Error stringifying error:', jsonError);
        }

        throw new Error(`Failed to delete contractor: ${errorDetails}`);
      }
    }
  });
