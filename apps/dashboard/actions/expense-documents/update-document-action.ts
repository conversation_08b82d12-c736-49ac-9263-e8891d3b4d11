'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import {
  updateExpenseDocument,
  getExpenseDocument,
  DocumentType,
  PaymentMethod,
  DocumentStatus,
  PaymentStatus,
  SourceType,
  DocumentExpenseStatus,
  BusinessExpenseStatus
} from '@workspace/database/queries';
import { replaceOrgSlug, routes } from '@workspace/routes';
import { authOrganizationActionClient } from '~/actions/safe-action';

// Schema for updating an expense document
const updateDocumentSchema = z.object({
  id: z.string().uuid(),
  documentType: z.nativeEnum(DocumentType).optional(),
  invoiceNumber: z.string().min(1, 'Invoice number is required').optional(),
  issueDate: z.date().optional(),
  dueDate: z.date().optional(),
  saleDate: z.date().optional(),
  netAmount: z.number().nonnegative('Net amount must be non-negative').optional(),
  vatAmount: z.number().nonnegative('VAT amount must be non-negative').optional(),
  vatPercentage: z.number().nonnegative('VAT percentage must be non-negative').optional(),
  grossAmount: z.number().nonnegative('Gross amount must be non-negative').optional(),
  currency: z.string().length(3, 'Currency must be a 3-letter code').optional(),
  plnAmount: z.number().nonnegative('PLN amount must be non-negative').optional(),
  vendorName: z.string().min(1, 'Vendor name is required').optional(),
  vendorVatId: z.string().nullable().optional(),
  contractorId: z.string().uuid().nullable().optional(),
  paymentMethod: z.nativeEnum(PaymentMethod).optional(),
  paymentAccountNumber: z.string().nullable().optional(),
  paymentDeadlineExceeded: z.boolean().optional(),
  categoryId: z.string().uuid().nullable().optional(),
  isCompanyExpense: z.nativeEnum(DocumentExpenseStatus).optional(),
  status: z.nativeEnum(DocumentStatus).optional(),
  paymentStatus: z.nativeEnum(PaymentStatus).optional(),
  documentPdfUrl: z.string().url().nullable().optional(),
  sourceType: z.nativeEnum(SourceType).optional(),
  sourceDetails: z.string().nullable().optional(),
  transactionId: z.string().uuid().nullable().optional(),
  subscriptionId: z.string().uuid().nullable().optional(),
  frequency: z.string().nullable().optional(),
  lineItems: z.array(z.object({
    id: z.string().uuid().optional(),
    description: z.string().min(1, 'Description is required'),
    quantity: z.number().positive('Quantity must be positive'),
    unitPrice: z.number().nonnegative('Unit price must be non-negative'),
    netAmount: z.number().nonnegative('Net amount must be non-negative'),
    vatRate: z.number().nonnegative('VAT rate must be non-negative'),
    vatAmount: z.number().nonnegative('VAT amount must be non-negative'),
    grossAmount: z.number().nonnegative('Gross amount must be non-negative'),
    isBusinessExpense: z.nativeEnum(BusinessExpenseStatus).optional(),
  })).optional(),
});

/**
 * Server action for updating an expense document
 */
export const updateExpenseDocumentAction = authOrganizationActionClient
  .metadata({ actionName: 'updateExpenseDocumentAction' })
  .schema(updateDocumentSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const { id, ...data } = parsedInput;

      // Verify the document belongs to the organization
      const existingDocument = await getExpenseDocument(id, ctx.organization.id);
      
      if (!existingDocument) {
        return {
          success: false,
          error: 'Document not found or does not belong to this organization'
        };
      }

      // Update the document
      const document = await updateExpenseDocument(id, data);

      if (!document) {
        return {
          success: false,
          error: 'Failed to update document'
        };
      }

      // Revalidate the expense documents page and the single document page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.invoices.Expenses,
          ctx.organization.slug
        )
      );
      
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.invoices.Id,
          ctx.organization.slug
        ).replace('[id]', id)
      );

      return { success: true, document };
    } catch (error) {
      console.error('Error updating expense document:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update expense document' 
      };
    }
  });
