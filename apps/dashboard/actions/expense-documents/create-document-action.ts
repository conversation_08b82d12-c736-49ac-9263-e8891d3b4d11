'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import {
  createExpenseDocument,
  DocumentType,
  PaymentMethod,
  DocumentStatus,
  PaymentStatus,
  SourceType,
  DocumentExpenseStatus,
  BusinessExpenseStatus
} from '@workspace/database/queries';
import { replaceOrgSlug, routes } from '@workspace/routes';
import { authOrganizationActionClient } from '~/actions/safe-action';

// Schema for creating an expense document
const createDocumentSchema = z.object({
  documentType: z.nativeEnum(DocumentType),
  invoiceNumber: z.string().min(1, 'Invoice number is required'),
  issueDate: z.date(),
  dueDate: z.date(),
  saleDate: z.date(),
  netAmount: z.number().nonnegative('Net amount must be non-negative'),
  vatAmount: z.number().nonnegative('VAT amount must be non-negative'),
  vatPercentage: z.number().nonnegative('VAT percentage must be non-negative'),
  grossAmount: z.number().nonnegative('Gross amount must be non-negative'),
  currency: z.string().length(3, 'Currency must be a 3-letter code'),
  plnAmount: z.number().nonnegative('PLN amount must be non-negative'),
  vendorName: z.string().min(1, 'Vendor name is required'),
  vendorVatId: z.string().nullable().optional(),
  contractorId: z.string().uuid().nullable().optional(),
  paymentMethod: z.nativeEnum(PaymentMethod),
  paymentAccountNumber: z.string().nullable().optional(),
  paymentDeadlineExceeded: z.boolean().optional(),
  categoryId: z.string().uuid().nullable().optional(),
  isCompanyExpense: z.nativeEnum(DocumentExpenseStatus).optional(),
  status: z.nativeEnum(DocumentStatus).optional(),
  paymentStatus: z.nativeEnum(PaymentStatus).optional(),
  documentPdfUrl: z.string().url().nullable().optional(),
  sourceType: z.nativeEnum(SourceType).optional(),
  sourceDetails: z.string().nullable().optional(),
  transactionId: z.string().uuid().nullable().optional(),
  subscriptionId: z.string().uuid().nullable().optional(),
  frequency: z.string().nullable().optional(),
  lineItems: z.array(z.object({
    description: z.string().min(1, 'Description is required'),
    quantity: z.number().positive('Quantity must be positive'),
    unitPrice: z.number().nonnegative('Unit price must be non-negative'),
    netAmount: z.number().nonnegative('Net amount must be non-negative'),
    vatRate: z.number().nonnegative('VAT rate must be non-negative'),
    vatAmount: z.number().nonnegative('VAT amount must be non-negative'),
    grossAmount: z.number().nonnegative('Gross amount must be non-negative'),
    isBusinessExpense: z.nativeEnum(BusinessExpenseStatus).optional(),
  })).optional(),
});

/**
 * Server action for creating an expense document
 */
export const createExpenseDocumentAction = authOrganizationActionClient
  .metadata({ actionName: 'createExpenseDocumentAction' })
  .schema(createDocumentSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Helper function to map string value to enum key
    function mapStringValueToEnumKey<TEnum extends Record<string, string>>(
      stringValue: string | undefined | null,
      enumObject: TEnum,
      fieldName: string // For logging purposes
    ): keyof TEnum | undefined | { error: true; message: string; type: string } {
      if (stringValue === undefined || stringValue === null) {
        return undefined;
      }
      const enumKey = (Object.keys(enumObject) as Array<keyof TEnum>).find(
        (key) => enumObject[key] === stringValue
      );

      if (!enumKey) {
        console.error(`Critical: Could not map ${fieldName} value "${stringValue}" to a valid Prisma enum key.`);
        return { error: true, message: `Internal server error: Invalid ${fieldName} configuration.`, type: 'EnumMappingError' };
      }
      return enumKey;
    }

    // Helper function to map enum key back to string value for client
    function mapEnumKeyToStringValue<TEnum extends Record<string, string>>(
      enumKey: keyof TEnum | undefined | null,
      enumObject: TEnum,
      fieldName: string // For logging purposes
    ): string | undefined {
      if (enumKey === undefined || enumKey === null) {
        return undefined;
      }
      const stringValue = enumObject[enumKey];
      if (stringValue === undefined) {
        // This should not happen if the enumKey is valid
        console.warn(`Warning: Could not map ${fieldName} enum key "${String(enumKey)}" back to a string value.`);
        return String(enumKey); // Fallback, though ideally UI handles this
      }
      return stringValue;
    }

    try {
      // Log the input data for debugging
      console.log('Creating expense document with input:', {
        organizationId: ctx.organization.id,
        ...parsedInput
      });

      // --- Map all enum string values to Prisma enum keys ---

      const documentTypeKeyResult = mapStringValueToEnumKey(parsedInput.documentType, DocumentType, 'documentType');
      if (documentTypeKeyResult && typeof documentTypeKeyResult === 'object' && documentTypeKeyResult.error) return { success: false, error: documentTypeKeyResult.message, errorType: documentTypeKeyResult.type };
      const documentTypeKey = documentTypeKeyResult as keyof typeof DocumentType | undefined;
      if (!documentTypeKey) { // documentType is required
        console.error(`Critical: documentType is required but mapping resulted in undefined.`);
        return { success: false, error: 'Internal server error: documentType is required.', errorType: 'EnumMappingError' };
      }


      const paymentMethodKeyResult = mapStringValueToEnumKey(parsedInput.paymentMethod, PaymentMethod, 'paymentMethod');
      if (paymentMethodKeyResult && typeof paymentMethodKeyResult === 'object' && paymentMethodKeyResult.error) return { success: false, error: paymentMethodKeyResult.message, errorType: paymentMethodKeyResult.type };
      const paymentMethodKey = paymentMethodKeyResult as keyof typeof PaymentMethod | undefined;
      if (!paymentMethodKey) { // paymentMethod is required
         console.error(`Critical: paymentMethod is required but mapping resulted in undefined.`);
        return { success: false, error: 'Internal server error: paymentMethod is required.', errorType: 'EnumMappingError' };
      }

      // Determine isCompanyExpense based on line items
      let derivedIsCompanyExpenseStringValue: string | undefined = parsedInput.isCompanyExpense;

      if (parsedInput.lineItems && parsedInput.lineItems.length > 0) {
        const lineItemStatuses = parsedInput.lineItems.map(item => item.isBusinessExpense).filter(Boolean) as string[]; // Filter out undefined/null

        if (lineItemStatuses.some(status => status === BusinessExpenseStatus.VERIFICATION)) {
          derivedIsCompanyExpenseStringValue = DocumentExpenseStatus.NEED_VERIFICATION;
        } else if (lineItemStatuses.every(status => status === BusinessExpenseStatus.YES)) {
          derivedIsCompanyExpenseStringValue = DocumentExpenseStatus.YES;
        } else if (lineItemStatuses.every(status => status === BusinessExpenseStatus.NO)) {
          derivedIsCompanyExpenseStringValue = DocumentExpenseStatus.NO;
        } else if (lineItemStatuses.some(status => status === BusinessExpenseStatus.YES) && lineItemStatuses.some(status => status === BusinessExpenseStatus.NO)) {
          derivedIsCompanyExpenseStringValue = DocumentExpenseStatus.PARTIAL;
        }
        // If none of the above (e.g., empty lineItemStatuses after filter, or mixed statuses not covered),
        // it will retain parsedInput.isCompanyExpense or be undefined,
        // which then might default to NEED_VERIFICATION in the DB or based on mapping.
        // If all items are undefined for isBusinessExpense, derivedIsCompanyExpenseStringValue remains parsedInput.isCompanyExpense
      }

      console.log('Derived isCompanyExpense string value:', derivedIsCompanyExpenseStringValue);

      const isCompanyExpenseKeyResult = mapStringValueToEnumKey(derivedIsCompanyExpenseStringValue, DocumentExpenseStatus, 'isCompanyExpense');
      if (isCompanyExpenseKeyResult && typeof isCompanyExpenseKeyResult === 'object' && isCompanyExpenseKeyResult.error) return { success: false, error: isCompanyExpenseKeyResult.message, errorType: isCompanyExpenseKeyResult.type };
      const isCompanyExpenseKey = isCompanyExpenseKeyResult as keyof typeof DocumentExpenseStatus | undefined;

      const statusKeyResult = mapStringValueToEnumKey(parsedInput.status, DocumentStatus, 'status');
      if (statusKeyResult && typeof statusKeyResult === 'object' && statusKeyResult.error) return { success: false, error: statusKeyResult.message, errorType: statusKeyResult.type };
      const statusKey = statusKeyResult as keyof typeof DocumentStatus | undefined;

      const paymentStatusKeyResult = mapStringValueToEnumKey(parsedInput.paymentStatus, PaymentStatus, 'paymentStatus');
      if (paymentStatusKeyResult && typeof paymentStatusKeyResult === 'object' && paymentStatusKeyResult.error) return { success: false, error: paymentStatusKeyResult.message, errorType: paymentStatusKeyResult.type };
      const paymentStatusKey = paymentStatusKeyResult as keyof typeof PaymentStatus | undefined;

      const sourceTypeKeyResult = mapStringValueToEnumKey(parsedInput.sourceType, SourceType, 'sourceType');
      if (sourceTypeKeyResult && typeof sourceTypeKeyResult === 'object' && sourceTypeKeyResult.error) return { success: false, error: sourceTypeKeyResult.message, errorType: sourceTypeKeyResult.type };
      const sourceTypeKey = sourceTypeKeyResult as keyof typeof SourceType | undefined;

      let mappedLineItems;
      if (parsedInput.lineItems) {
        mappedLineItems = [];
        for (const item of parsedInput.lineItems) {
          const isBusinessExpenseKeyResult = mapStringValueToEnumKey(item.isBusinessExpense, BusinessExpenseStatus, 'lineItems[].isBusinessExpense');
          if (isBusinessExpenseKeyResult && typeof isBusinessExpenseKeyResult === 'object' && isBusinessExpenseKeyResult.error) return { success: false, error: isBusinessExpenseKeyResult.message, errorType: isBusinessExpenseKeyResult.type };

          mappedLineItems.push({
            ...item,
            isBusinessExpense: isBusinessExpenseKeyResult as keyof typeof BusinessExpenseStatus | undefined,
          });
        }
      }

      const documentDataForDb = {
        ...parsedInput,
        documentType: documentTypeKey,
        paymentMethod: paymentMethodKey,
        isCompanyExpense: isCompanyExpenseKey,
        status: statusKey,
        paymentStatus: paymentStatusKey,
        sourceType: sourceTypeKey,
        lineItems: mappedLineItems,
      };

      console.log('Data being sent to createExpenseDocument:', documentDataForDb);

      const document = await createExpenseDocument({
        organizationId: ctx.organization.id,
        ...documentDataForDb,
      });

      // Log success
      if (!document) {
        console.error('Failed to create expense document: document is null');
        return {
          success: false,
          error: 'Failed to create expense document',
          errorType: 'DocumentCreationError'
        };
      }

      console.log('Expense document created successfully:', document.id);

      // Revalidate the expense documents page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.invoices.Expenses,
          ctx.organization.slug
        )
      );

      // Define a type for Decimal.js-like objects
      interface DecimalLike {
        s: number;
        e: number;
        d: number[];
        toNumber: () => number;
      }

      // Type for objects that might contain Decimal values
      type SerializableObject = Record<string, unknown>;

      // Serialize Decimal fields to numbers before returning to the client
      const serializeDecimals = (obj: unknown): unknown => {
        if (obj === null || typeof obj !== 'object') {
          return obj;
        }

        // More robust check for Decimal.js-like objects (Prisma uses Decimal.js)
        // Checks for characteristic properties (s, e, d) and the toNumber method.
        // This is more resilient to minification of constructor names.
        const objAsRecord = obj as Record<string, unknown>;

        // Check if object has properties that match a Decimal.js object
        if (
          'toNumber' in objAsRecord &&
          typeof objAsRecord.toNumber === 'function' &&
          's' in objAsRecord &&
          typeof objAsRecord.s === 'number' &&
          'e' in objAsRecord &&
          typeof objAsRecord.e === 'number' &&
          'd' in objAsRecord &&
          Array.isArray(objAsRecord.d)
        ) {
          // Safe to cast to DecimalLike now that we've verified the structure
          return (objAsRecord as unknown as DecimalLike).toNumber();
        }

        if (Array.isArray(obj)) {
          return obj.map(serializeDecimals);
        }

        const newObj: SerializableObject = {};
        for (const key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            newObj[key] = serializeDecimals((obj as Record<string, unknown>)[key]);
          }
        }
        return newObj;
      };
      // Removed erroneous }; that was here
      console.log('Document from DB - status:', document.status, 'paymentStatus:', document.paymentStatus);

      // --- Prepare document for client: Map enum keys back to string values ---
      const mappedClientDocumentType = mapEnumKeyToStringValue(document.documentType as keyof typeof DocumentType, DocumentType, 'documentType');
      const mappedClientPaymentMethod = mapEnumKeyToStringValue(document.paymentMethod as keyof typeof PaymentMethod, PaymentMethod, 'paymentMethod');
      const mappedClientStatus = mapEnumKeyToStringValue(document.status as keyof typeof DocumentStatus, DocumentStatus, 'status');
      const mappedClientPaymentStatus = mapEnumKeyToStringValue(document.paymentStatus as keyof typeof PaymentStatus, PaymentStatus, 'paymentStatus');
      const mappedClientIsCompanyExpense = mapEnumKeyToStringValue(document.isCompanyExpense as keyof typeof DocumentExpenseStatus | undefined, DocumentExpenseStatus, 'isCompanyExpense');
      const mappedClientSourceType = mapEnumKeyToStringValue(document.sourceType as keyof typeof SourceType | undefined, SourceType, 'sourceType');

      console.log('Mapped for client - status:', mappedClientStatus, 'paymentStatus:', mappedClientPaymentStatus);

      const documentForClient = {
        ...document,
        documentType: mappedClientDocumentType,
        paymentMethod: mappedClientPaymentMethod,
        status: mappedClientStatus,
        paymentStatus: mappedClientPaymentStatus,
        isCompanyExpense: mappedClientIsCompanyExpense,
        sourceType: mappedClientSourceType,
        lineItems: document.lineItems?.map(item => {
          const mappedLineItemIsBusinessExpense = mapEnumKeyToStringValue(
            item.isBusinessExpense as keyof typeof BusinessExpenseStatus | undefined,
            BusinessExpenseStatus,
            'lineItems[].isBusinessExpense'
          );
          return {
            ...item,
            isBusinessExpense: mappedLineItemIsBusinessExpense,
          };
        }),
      };

      const serializableDocument = serializeDecimals(documentForClient);
      console.log('Document being returned to client:', serializableDocument);

      return { success: true, document: serializableDocument };
    } catch (error) {
      // Enhanced error logging
      console.error('Error creating expense document:', error);

      // Log the stack trace if available
      if (error instanceof Error && error.stack) {
        console.error('Error stack trace:', error.stack);
      }

      // Define interfaces for error types
      interface PrismaErrorWithCode {
        code: string;
      }

      interface PrismaErrorWithMeta {
        meta: {
          target?: string | string[];
        };
      }

      interface PrismaErrorWithClientVersion {
        clientVersion: string;
      }

      // Deeper inspection of the error object
      console.error('Raw error object (stringified with properties):', JSON.stringify(error, Object.getOwnPropertyNames(error)));
      if (error && typeof error === 'object') {
        console.error('Error object keys:', Object.keys(error));
        // Check for common Prisma error properties
        if ('code' in error) {
          console.error('Error code (if present):', (error as PrismaErrorWithCode).code);
        }
        if ('meta' in error) {
          const errorWithMeta = error as PrismaErrorWithMeta;
          if (errorWithMeta.meta && typeof errorWithMeta.meta === 'object' && 'target' in errorWithMeta.meta) {
            console.error('Error meta target (if present, e.g., Prisma):', errorWithMeta.meta.target);
          }
        }
        if ('clientVersion' in error) {
          console.error('Error clientVersion (if present, e.g., Prisma):', (error as PrismaErrorWithClientVersion).clientVersion);
        }
      }

      // Log the input data that caused the error (excluding sensitive data)
      console.error('Input data that caused the error:', {
        documentType: parsedInput.documentType,
        invoiceNumber: parsedInput.invoiceNumber,
        hasLineItems: !!parsedInput.lineItems,
        lineItemsCount: parsedInput.lineItems?.length || 0
      });

      // Return detailed error information
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create expense document',
        errorType: error instanceof Error ? error.constructor.name : 'Unknown',
        // Include additional context that might help debugging
        context: {
          documentType: parsedInput.documentType,
          hasLineItems: !!parsedInput.lineItems
        }
      };
    }
  });
