'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { deleteExpenseDocument, getExpenseDocument } from '@workspace/database/queries';
import { replaceOrgSlug, routes } from '@workspace/routes';
import { authOrganizationActionClient } from '~/actions/safe-action';

// Schema for deleting an expense document
const deleteDocumentSchema = z.object({
  id: z.string().uuid(),
});

/**
 * Server action for deleting an expense document
 */
export const deleteExpenseDocumentAction = authOrganizationActionClient
  .metadata({ actionName: 'deleteExpenseDocumentAction' })
  .schema(deleteDocumentSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const { id } = parsedInput;

      // Verify the document belongs to the organization
      const document = await getExpenseDocument(id, ctx.organization.id);

      if (!document) {
        return { 
          success: false, 
          error: 'Document not found or does not belong to this organization' 
        };
      }

      // Delete the document
      const result = await deleteExpenseDocument(id);

      if (!result) {
        return {
          success: false,
          error: 'Failed to delete document'
        };
      }

      // Revalidate the expense documents page
      revalidatePath(
        replaceOrgSlug(
          routes.dashboard.organizations.slug.invoices.Expenses,
          ctx.organization.slug
        )
      );

      return { success: true };
    } catch (error) {
      console.error('Error deleting expense document:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to delete expense document' 
      };
    }
  });
