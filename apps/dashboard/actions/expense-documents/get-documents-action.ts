'use server';

import { z } from 'zod';
import {
  getExpenseDocuments,
  getExpenseDocument,
  DocumentType,
  DocumentStatus,
  PaymentStatus,
  PaymentMethod, // Added
  SourceType, // Added
  DocumentExpenseStatus, // Added
  BusinessExpenseStatus, // Added
} from '@workspace/database/queries';
import { authOrganizationActionClient } from '~/actions/safe-action';

// Helper function to map string value to enum key for DB queries
function mapStringValueToEnumKey<TEnum extends Record<string, string>>(
  stringValue: string | undefined | null,
  enumObject: TEnum,
  fieldName: string // For logging purposes
): keyof TEnum | undefined | null | { error: true; message: string; type: string } { // Allow null to be returned if input is null
  if (stringValue === undefined) {
    return undefined;
  }
  if (stringValue === null) {
    return null;
  }
  const enumKey = (Object.keys(enumObject) as Array<keyof TEnum>).find(
    (key) => enumObject[key] === stringValue
  );

  if (!enumKey) {
    console.error(`Critical: Could not map ${fieldName} value "${stringValue}" to a valid Prisma enum key in getExpenseDocumentsAction.`);
    // Return an error object or throw, depending on desired handling for filter mapping failure
    return { error: true, message: `Internal server error: Invalid ${fieldName} filter configuration.`, type: 'EnumMappingError' };
  }
  return enumKey;
}

// Helper function to map enum key back to string value for client
function mapEnumKeyToStringValue<TEnum extends Record<string, string>>(
  enumKey: keyof TEnum | undefined | null,
  enumObject: TEnum,
  fieldName: string // For logging purposes
): string | undefined {
  if (enumKey === undefined || enumKey === null) {
    return undefined;
  }
  const stringValue = enumObject[enumKey];
  if (stringValue === undefined) {
    console.warn(`Warning: Could not map ${fieldName} enum key "${String(enumKey)}" back to a string value in getExpenseDocumentsAction.`);
    return String(enumKey); // Fallback
  }
  return stringValue;
}

// Define types for expense document and line item
interface Contractor {
  id: string;
  shortName: string;
  fullName: string;
  [key: string]: unknown;
}

interface Category {
  id: string;
  name: string;
  color: string;
  [key: string]: unknown;
}

interface Transaction {
  id: string;
  amount: number | string;
  description: string;
  [key: string]: unknown;
}

interface Subscription {
  id: string;
  name: string;
  [key: string]: unknown;
}

type ExpenseDocumentLineItem = {
  id: string;
  documentId: string;
  description: string;
  quantity: number | string;
  unitPrice: number | string;
  netAmount: number | string;
  vatRate: number | string;
  vatAmount: number | string;
  grossAmount: number | string;
  isBusinessExpense: string;
  createdAt: Date;
  updatedAt: Date;
  document?: ExpenseDocument;
};

type ExpenseDocument = {
  id: string;
  organizationId: string;
  documentType: string;
  invoiceNumber: string;
  issueDate: Date;
  dueDate: Date;
  saleDate: Date;
  netAmount: number | string;
  vatAmount: number | string;
  vatPercentage: number | string;
  grossAmount: number | string;
  currency: string;
  plnAmount: number | string;
  vendorName: string;
  vendorVatId: string | null;
  contractorId: string | null;
  paymentMethod: string;
  paymentAccountNumber: string | null;
  paymentDeadlineExceeded: boolean;
  categoryId: string | null;
  isCompanyExpense: string;
  status: string;
  paymentStatus: string;
  documentPdfUrl: string | null;
  sourceType: string;
  sourceDetails: string | null;
  transactionId: string | null;
  subscriptionId: string | null;
  frequency: string | null;
  createdAt: Date;
  updatedAt: Date;
  lineItems: ExpenseDocumentLineItem[];
  contractor?: Contractor | null;
  category?: Category | null;
  transaction?: Transaction | null;
  subscription?: Subscription | null;
};

// Schema for getting a list of expense documents
const getDocumentsSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  documentType: z.nativeEnum(DocumentType).optional(),
  status: z.nativeEnum(DocumentStatus).optional(),
  paymentStatus: z.nativeEnum(PaymentStatus).optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  contractorId: z.string().uuid().optional(),
  categoryId: z.string().uuid().optional(),
  search: z.string().optional(),
});

/**
 * Server action for getting a list of expense documents
 */
export const getExpenseDocumentsAction = authOrganizationActionClient
  .metadata({ actionName: 'getExpenseDocumentsAction' })
  .schema(getDocumentsSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      // Map filter enum string values to Prisma enum keys for the DB query
      const documentTypeKeyFilter = mapStringValueToEnumKey(parsedInput.documentType, DocumentType, 'filter:documentType');
      if (documentTypeKeyFilter && typeof documentTypeKeyFilter === 'object' && documentTypeKeyFilter.error) return { documents: [], pagination: { total: 0, totalPages: 0, page: 1, limit: 10, hasNextPage: false, hasPrevPage: false }, error: documentTypeKeyFilter.message };
      
      const statusKeyFilter = mapStringValueToEnumKey(parsedInput.status, DocumentStatus, 'filter:status');
      if (statusKeyFilter && typeof statusKeyFilter === 'object' && statusKeyFilter.error) return { documents: [], pagination: { total: 0, totalPages: 0, page: 1, limit: 10, hasNextPage: false, hasPrevPage: false }, error: statusKeyFilter.message };

      const paymentStatusKeyFilter = mapStringValueToEnumKey(parsedInput.paymentStatus, PaymentStatus, 'filter:paymentStatus');
      if (paymentStatusKeyFilter && typeof paymentStatusKeyFilter === 'object' && paymentStatusKeyFilter.error) return { documents: [], pagination: { total: 0, totalPages: 0, page: 1, limit: 10, hasNextPage: false, hasPrevPage: false }, error: paymentStatusKeyFilter.message };

      const filtersForDb = {
        ...parsedInput,
        documentType: documentTypeKeyFilter as keyof typeof DocumentType | undefined,
        status: statusKeyFilter as keyof typeof DocumentStatus | undefined,
        paymentStatus: paymentStatusKeyFilter as keyof typeof PaymentStatus | undefined,
      };

      const result = await getExpenseDocuments({
        organizationId: ctx.organization.id,
        ...filtersForDb,
      });

      // Process documents for client: map enums to strings and Decimals to strings
      const documents = result.documents.map((doc) => { // Removed ExpenseDocument type annotation for doc
        // doc here is the raw Prisma type
        const mappedDoc = {
          ...doc,
          // Map enum keys from DB to string values for client
          documentType: mapEnumKeyToStringValue(doc.documentType, DocumentType, 'documentType'),
          paymentMethod: mapEnumKeyToStringValue(doc.paymentMethod, PaymentMethod, 'paymentMethod'),
          status: mapEnumKeyToStringValue(doc.status, DocumentStatus, 'status'),
          paymentStatus: mapEnumKeyToStringValue(doc.paymentStatus, PaymentStatus, 'paymentStatus'),
          isCompanyExpense: mapEnumKeyToStringValue(doc.isCompanyExpense, DocumentExpenseStatus, 'isCompanyExpense'),
          sourceType: mapEnumKeyToStringValue(doc.sourceType, SourceType, 'sourceType'),
          // Process line items
          lineItems: doc.lineItems.map((item) => ({ // Removed ExpenseDocumentLineItem type for item
            ...item,
            isBusinessExpense: mapEnumKeyToStringValue(item.isBusinessExpense, BusinessExpenseStatus, 'lineItems[].isBusinessExpense'),
            // Decimal to string conversion for line items
            quantity: item.quantity?.toString(),
            unitPrice: item.unitPrice?.toString(),
            netAmount: item.netAmount?.toString(),
            vatRate: item.vatRate?.toString(),
            vatAmount: item.vatAmount?.toString(),
            grossAmount: item.grossAmount?.toString(),
          })),
          // Decimal to string conversion for document
          netAmount: doc.netAmount?.toString(),
          vatAmount: doc.vatAmount?.toString(),
          vatPercentage: doc.vatPercentage?.toString(),
          grossAmount: doc.grossAmount?.toString(),
          plnAmount: doc.plnAmount?.toString(),
        };
        return mappedDoc as unknown as ExpenseDocument; // Cast to expected client type after transformation
      });

      return {
        documents,
        pagination: result.pagination,
      };
    } catch (error) {
      console.error('Error fetching expense documents:', error);
      return {
        documents: [],
        pagination: {
          page: parsedInput.page || 1,
          limit: parsedInput.limit || 10,
          total: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false,
        },
      };
    }
  });

// Schema for getting a single expense document
const getDocumentSchema = z.object({
  id: z.string().uuid(),
});

/**
 * Server action for getting a single expense document
 */
export const getExpenseDocumentAction = authOrganizationActionClient
  .metadata({ actionName: 'getExpenseDocumentAction' })
  .schema(getDocumentSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const document = await getExpenseDocument(parsedInput.id, ctx.organization.id);

      if (!document) {
        return {
          success: false,
          error: 'Document not found or does not belong to this organization'
        };
      }

      // Map enums and serialize Decimals for the single document
      const clientReadyDocument = {
        ...document,
        documentType: mapEnumKeyToStringValue(document.documentType, DocumentType, 'documentType'),
        paymentMethod: mapEnumKeyToStringValue(document.paymentMethod, PaymentMethod, 'paymentMethod'),
        status: mapEnumKeyToStringValue(document.status, DocumentStatus, 'status'),
        paymentStatus: mapEnumKeyToStringValue(document.paymentStatus, PaymentStatus, 'paymentStatus'),
        isCompanyExpense: mapEnumKeyToStringValue(document.isCompanyExpense, DocumentExpenseStatus, 'isCompanyExpense'),
        sourceType: mapEnumKeyToStringValue(document.sourceType, SourceType, 'sourceType'),
        netAmount: document.netAmount?.toString(),
        vatAmount: document.vatAmount?.toString(),
        vatPercentage: document.vatPercentage?.toString(),
        grossAmount: document.grossAmount?.toString(),
        plnAmount: document.plnAmount?.toString(),
        lineItems: document.lineItems.map((item) => ({ // Removed ExpenseDocumentLineItem type for item
          ...item,
          isBusinessExpense: mapEnumKeyToStringValue(item.isBusinessExpense, BusinessExpenseStatus, 'lineItems[].isBusinessExpense'),
          quantity: item.quantity?.toString(),
          unitPrice: item.unitPrice?.toString(),
          netAmount: item.netAmount?.toString(),
          vatRate: item.vatRate?.toString(),
          vatAmount: item.vatAmount?.toString(),
          grossAmount: item.grossAmount?.toString(),
        })),
      };

      return { success: true, document: clientReadyDocument as unknown as ExpenseDocument }; // Cast to expected client type
    } catch (error) {
      console.error('Error fetching expense document:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch expense document'
      };
    }
  });
