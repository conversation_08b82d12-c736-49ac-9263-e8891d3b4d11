import * as React from 'react';
import { notFound } from 'next/navigation';
import { type Metadata } from 'next';
import { format } from 'date-fns';
import { ArrowLeftIcon, CalendarIcon, FileTextIcon, TagIcon, UserIcon } from 'lucide-react';

import { But<PERSON> } from '@workspace/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Separator } from '@workspace/ui/components/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@workspace/ui/components/table';

import { getDocumentAction } from '~/actions/financial-documents';
import { getExpenseDocumentAction } from '~/actions/expense-documents';
import { createTitle } from '~/lib/formatters';
import { PdfActions } from '~/components/financial-documents/pdf-actions';
import Link from 'next/link';

interface PageProps {
  params: { slug: string; id: string };
}

export async function generateMetadata({
  params
}: PageProps): Promise<Metadata> {
  // Try to get the document
  const financialDocumentResult = await getDocumentAction({
    id: params.id
  });

  const expenseDocumentResult = await getExpenseDocumentAction({
    id: params.id
  });

  const document = financialDocumentResult.success 
    ? financialDocumentResult.document 
    : expenseDocumentResult.success 
      ? expenseDocumentResult.document 
      : null;

  if (!document) {
    return {
      title: createTitle('Dokument nie znaleziony')
    };
  }

  return {
    title: createTitle(`Dokument ${document.invoiceNumber}`)
  };
}

export default async function DocumentDetailPage({
  params
}: PageProps): Promise<React.JSX.Element> {
  // Try to get the document as a financial document first
  const financialDocumentResult = await getDocumentAction({
    id: params.id
  });

  // If not found, try to get it as an expense document
  const expenseDocumentResult = !financialDocumentResult.success 
    ? await getExpenseDocumentAction({ id: params.id }) 
    : { success: false };

  // If neither is found, return 404
  if (!financialDocumentResult.success && !expenseDocumentResult.success) {
    notFound();
  }

  // Use whichever document was found
  const document = financialDocumentResult.success 
    ? financialDocumentResult.document 
    : expenseDocumentResult.document;

  // Determine if this is an expense document
  const isExpense = !financialDocumentResult.success && expenseDocumentResult.success;

  // Format currency amounts
  const formatAmount = (amount: string | number) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return numAmount.toLocaleString('pl-PL', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  return (
    <div className="container py-6">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            asChild
          >
            <Link href={`/organizations/${params.slug}/invoices/${isExpense ? 'expenses' : 'income'}`}>
              <ArrowLeftIcon className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">
            {isExpense ? 'Dokument kosztowy' : 'Dokument przychodowy'}: {document.invoiceNumber}
          </h1>
        </div>
        <div className="flex gap-2">
          <PdfActions
            invoiceId={document.id}
            invoiceNumber={document.invoiceNumber}
            existingPdfUrl={document.documentPdfUrl}
          />
          <Button variant="outline" asChild>
            <Link href={`/organizations/${params.slug}/invoices/${params.id}/edit`}>
              Edytuj
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        {/* Document Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileTextIcon className="h-5 w-5" />
              Informacje o dokumencie
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-muted-foreground">Typ dokumentu</p>
              <p className="font-medium">
                {document.documentType === 'invoice' && 'Faktura'}
                {document.documentType === 'receipt' && 'Paragon'}
                {document.documentType === 'receipt_with_vat' && 'Paragon z NIP'}
                {document.documentType === 'correction_invoice' && 'Faktura korygująca'}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Numer dokumentu</p>
              <p className="font-medium">{document.invoiceNumber}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Status</p>
              <p className="font-medium">
                {document.status === 'draft' && 'Szkic'}
                {document.status === 'created' && 'Utworzony'}
                {document.status === 'created_and_sent' && 'Wysłany'}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Status płatności</p>
              <p className="font-medium">
                {document.paymentStatus === 'unpaid' && 'Nieopłacony'}
                {document.paymentStatus === 'partially_paid' && 'Częściowo opłacony'}
                {document.paymentStatus === 'paid' && 'Opłacony'}
                {document.paymentStatus === 'overdue' && 'Zaległy'}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Metoda płatności</p>
              <p className="font-medium">
                {document.paymentMethod === 'transfer' && 'Przelew'}
                {document.paymentMethod === 'cash' && 'Gotówka'}
                {document.paymentMethod === 'card' && 'Karta'}
                {document.paymentMethod === 'barter' && 'Barter'}
                {document.paymentMethod === 'other' && 'Inna'}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Dates */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CalendarIcon className="h-5 w-5" />
              Daty
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-muted-foreground">Data wystawienia</p>
              <p className="font-medium">{format(new Date(document.issueDate), 'dd.MM.yyyy')}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Data sprzedaży</p>
              <p className="font-medium">{format(new Date(document.saleDate), 'dd.MM.yyyy')}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Termin płatności</p>
              <p className="font-medium">{format(new Date(document.dueDate), 'dd.MM.yyyy')}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Data utworzenia</p>
              <p className="font-medium">{format(new Date(document.createdAt), 'dd.MM.yyyy')}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Ostatnia aktualizacja</p>
              <p className="font-medium">{format(new Date(document.updatedAt), 'dd.MM.yyyy')}</p>
            </div>
          </CardContent>
        </Card>

        {/* Contractor/Client Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserIcon className="h-5 w-5" />
              {isExpense ? 'Informacje o sprzedawcy' : 'Informacje o kliencie'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-muted-foreground">Nazwa</p>
              <p className="font-medium">{isExpense ? document.vendorName : document.clientName}</p>
            </div>
            {isExpense && document.vendorVatId && (
              <div>
                <p className="text-sm text-muted-foreground">NIP</p>
                <p className="font-medium">{document.vendorVatId}</p>
              </div>
            )}
            {!isExpense && document.clientVatId && (
              <div>
                <p className="text-sm text-muted-foreground">NIP</p>
                <p className="font-medium">{document.clientVatId}</p>
              </div>
            )}
            {document.contractor && (
              <div>
                <p className="text-sm text-muted-foreground">Kontrahent</p>
                <p className="font-medium">{document.contractor.shortName || document.contractor.fullName}</p>
              </div>
            )}
            {document.category && (
              <div>
                <p className="text-sm text-muted-foreground">Kategoria</p>
                <p className="font-medium">{document.category.name}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Financial Summary */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Podsumowanie finansowe</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <div>
              <p className="text-sm text-muted-foreground">Kwota netto</p>
              <p className="text-xl font-medium">
                {formatAmount(document.netAmount)} {document.currency}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Kwota VAT</p>
              <p className="text-xl font-medium">
                {formatAmount(document.vatAmount)} {document.currency}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Kwota brutto</p>
              <p className="text-xl font-medium">
                {formatAmount(document.grossAmount)} {document.currency}
              </p>
            </div>
            {document.currency !== 'PLN' && (
              <div>
                <p className="text-sm text-muted-foreground">Kwota w PLN</p>
                <p className="text-xl font-medium">
                  {formatAmount(document.plnAmount)} PLN
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Line Items */}
      {document.lineItems && document.lineItems.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Pozycje</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Opis</TableHead>
                  <TableHead className="text-right">Ilość</TableHead>
                  <TableHead className="text-right">Cena jedn.</TableHead>
                  <TableHead className="text-right">Netto</TableHead>
                  <TableHead className="text-right">VAT %</TableHead>
                  <TableHead className="text-right">VAT</TableHead>
                  <TableHead className="text-right">Brutto</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {document.lineItems.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.description}</TableCell>
                    <TableCell className="text-right">{formatAmount(item.quantity)}</TableCell>
                    <TableCell className="text-right">{formatAmount(item.unitPrice)} {document.currency}</TableCell>
                    <TableCell className="text-right">{formatAmount(item.netAmount)} {document.currency}</TableCell>
                    <TableCell className="text-right">{formatAmount(item.vatRate)}%</TableCell>
                    <TableCell className="text-right">{formatAmount(item.vatAmount)} {document.currency}</TableCell>
                    <TableCell className="text-right">{formatAmount(item.grossAmount)} {document.currency}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
