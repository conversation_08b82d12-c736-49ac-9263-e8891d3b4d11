import * as React from 'react';
import { type Metadata } from 'next';

import { createTitle } from '~/lib/formatters';
import { getContractorsServer } from '~/actions/contractors';
import { getOrganizationDetails } from '~/data/organization/get-organization-details';
import { getOrganizationLogo } from '~/data/organization/get-organization-logo';
import { InvoiceForm } from '~/components/financial-documents/invoice-form';

export const metadata: Metadata = {
  title: createTitle('Wystaw fakturę')
};

interface PageProps {
  params: { slug: string };
}

export default async function CreateInvoicePage({
  params
}: PageProps): Promise<React.JSX.Element> {
  // Fetch required data for the form
  const [contractors, organizationDetails, organizationLogo] = await Promise.all([
    getContractorsServer(),
    getOrganizationDetails(),
    getOrganizationLogo()
  ]);

  return (
    <div className="container py-6 min-h-screen">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Wystaw fakturę</h1>
      </div>

      <div className="max-w-none">
        <InvoiceForm
          contractors={contractors}
          organizationDetails={organizationDetails}
          organizationLogo={organizationLogo}
        />
      </div>
    </div>
  );
}
